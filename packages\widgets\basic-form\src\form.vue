<template>
  <widget-wrapper>
    <el-form ref="formRef" :model="formModel" label-width="100px">
      <el-form-item label="姓名" prop="name">
        <!-- <el-input v-model="formData.name" placeholder="请输入姓名"></el-input> -->
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">{{
          formConfig.submitText || '提交'
        }}</el-button>
        <el-button @click="resetForm">{{
          formConfig.resetText || '重置'
        }}</el-button>
        <el-button @click="cancelForm">{{
          formConfig.cancelText || '取消'
        }}</el-button>
      </el-form-item>
    </el-form>
  </widget-wrapper>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import WidgetWrapper from '@neue-plus/components/widget-wrapper'
import type { FormConfig } from './form'

defineOptions({
  name: 'BasicForm',
  inheritAttrs: false,
})

const props = defineProps({
  config: {
    type: Object as FormConfig,
    default: () => ({}),
  },
  formItems: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['formSubmit', 'formReset', 'formCancel'])

const formConfig = ref<FormConfig>({
  layout: 'horizontal',
  submitText: '提交',
  resetText: '重置',
  cancelText: '取消',
  showSubmit: true,
  showReset: false,
  showCancel: true,
  buttonAlign: 'center',
})
const formRef = ref()
const formModel = ref({
  name: '1',
})
// const formRules = ref({
//   name: [
//     { required: true, message: '请输入姓名', trigger: 'blur' },
//     { min: 2, max: 10, message: '长度在 2 到 10 个字符之间', trigger: 'blur' }
//   ]
// })

const submitForm = () => {
  // formRef.value.validate((valid: boolean) => {
  //   if (valid) {
  //     console.log('提交成功:', formData.value)
  //   } else {
  //     console.error('表单验证失败')
  //     return false
  //   }
  // })
  emit('formSubmit', {
    res: true,
    formData: formModel.value,
  })
  console.log('表单已提交:', formModel.value)
}

const resetForm = () => {
  formRef.value.resetFields()
  emit('formReset')
}

const cancelForm = () => {
  formRef.value.resetFields()
  console.log('表单已取消')
  emit('formCancel')
}

watch(
  () => props.config,
  (newConfig: FormConfig) => {
    console.log('配置已更新:', newConfig)
    // 可以在这里处理配置更新逻辑
    formConfig.value = { ...formConfig.value, ...newConfig }
  },
  { deep: true }
)

// defineExpose({

// })
</script>

<style scoped />
