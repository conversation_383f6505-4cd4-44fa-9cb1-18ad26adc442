# Neue CDP Space 技术架构文档

## 🏗️ 整体架构

```mermaid
graph TB
    subgraph "应用层"
        A[低代码设计器] --> B[MaterialRender 渲染引擎]
        C[业务应用] --> B
    end
    
    subgraph "组件层"
        B --> D[基础组件库<br/>@neue-plus/components]
        B --> E[业务组件库<br/>@neue-plus/widgets]
        D --> F[Element Plus]
    end
    
    subgraph "服务层"
        B --> G[事件流引擎<br/>EventFlow]
        B --> H[API 服务层<br/>@neue-plus/services]
        G --> I[组件实例管理]
        H --> J[REST API]
        H --> K[OData 协议]
    end
    
    subgraph "工具层"
        L[构建工具链<br/>@neue-plus/build] --> M[Rollup + Gulp]
        N[组件解析器<br/>@neue-plus/resolver] --> O[自动导入]
        P[主题系统<br/>@neue-plus/theme-chalk] --> Q[Sass 样式]
    end
    
    subgraph "基础层"
        R[Vue 3 + TypeScript] --> S[Composition API]
        T[pnpm Workspace] --> U[Monorepo 管理]
    end
```

## 📦 包依赖关系

```mermaid
graph LR
    A[neue-plus] --> B[@neue-plus/components]
    A --> C[@neue-plus/widgets]
    A --> D[@neue-plus/hooks]
    A --> E[@neue-plus/utils]
    A --> F[@neue-plus/constants]
    A --> G[@neue-plus/directives]
    A --> H[@neue-plus/locale]
    A --> I[@neue-plus/theme-chalk]
    
    B --> D
    B --> E
    B --> F
    C --> B
    C --> D
    
    J[@neue-plus/resolver] --> A
    K[@neue-plus/services] --> E
    
    L[internal/build] --> M[internal/build-utils]
    L --> N[internal/build-constants]
```

## 🔄 EventFlow 事件流架构

```mermaid
sequenceDiagram
    participant App as 应用层
    participant Provider as EventContext Provider
    participant Flow as EventFlow Engine
    participant Component as 目标组件
    
    App->>Provider: provideEventContext(handlers)
    Provider->>Provider: 创建 refs 容器
    Provider->>Provider: 注入 handlers 和 refs
    
    App->>Flow: eventFlow.run(actions, params)
    Flow->>Flow: 构建链表结构
    
    loop 执行事件链
        Flow->>Flow: 解析 actionType 和 target
        Flow->>Component: 调用组件方法或全局处理器
        Component-->>Flow: 返回执行结果
        Flow->>Flow: 处理延迟和下一个节点
    end
    
    Flow-->>App: 执行完成
```

## 🎨 MaterialRender 渲染流程

```mermaid
flowchart TD
    A[JSON 配置] --> B{解析配置}
    B --> C[全局配置<br/>ConfigProvider]
    B --> D[组件树<br/>Elements]
    B --> E[API 配置<br/>APIs]
    B --> F[事件配置<br/>Events]
    
    C --> G[NeConfigProvider]
    D --> H[递归渲染组件]
    E --> I[API 服务初始化]
    F --> J[事件流上下文]
    
    H --> K{组件类型}
    K -->|基础组件| L[NeButton, NeTable...]
    K -->|业务组件| M[WidgetBasicForm...]
    K -->|容器组件| N[WidgetContainer]
    
    L --> O[组件实例]
    M --> O
    N --> O
    
    O --> P[注册到 refs]
    O --> Q[绑定事件处理]
    
    I --> R[executeApi]
    J --> S[useEventFlow]
    
    P --> T[最终渲染]
    Q --> T
    R --> T
    S --> T
```

## 🛠️ 构建系统架构

```mermaid
graph TB
    subgraph "构建入口"
        A[gulpfile.ts] --> B[构建任务编排]
    end
    
    subgraph "并行构建任务"
        B --> C[buildModules<br/>模块构建]
        B --> D[buildFullBundle<br/>完整包构建]
        B --> E[generateTypesDefinitions<br/>类型定义生成]
        B --> F[buildHelper<br/>辅助工具构建]
        B --> G[buildThemeChalk<br/>样式构建]
    end
    
    subgraph "模块构建详情"
        C --> H[ESM 格式<br/>.mjs]
        C --> I[CommonJS 格式<br/>.js]
        C --> J[TypeScript 声明<br/>.d.ts]
    end
    
    subgraph "完整包构建"
        D --> K[UMD 格式<br/>浏览器使用]
        D --> L[ESM 格式<br/>现代打包工具]
        D --> M[压缩版本<br/>.min.js]
    end
    
    subgraph "输出目录"
        H --> N[dist/es/]
        I --> O[dist/lib/]
        J --> P[dist/types/]
        K --> Q[dist/]
        L --> Q
        M --> Q
    end
```

## 🔌 组件自动导入机制

```mermaid
sequenceDiagram
    participant Dev as 开发者
    participant Vite as Vite 构建
    participant Plugin as unplugin-vue-components
    participant Resolver as NeuePlusResolver
    participant Bundle as 最终包
    
    Dev->>Vite: 编写组件使用代码<br/><NeButton />
    Vite->>Plugin: 解析 SFC 模板
    Plugin->>Resolver: 查询组件信息<br/>resolveComponent('NeButton')
    
    Resolver->>Resolver: 匹配组件名称规则
    Resolver->>Resolver: 确定导入路径和样式
    Resolver-->>Plugin: 返回导入信息<br/>{from: 'neue-plus', name: 'NeButton'}
    
    Plugin->>Plugin: 生成导入语句<br/>import { NeButton } from 'neue-plus'
    Plugin-->>Vite: 返回转换后的代码
    Vite-->>Bundle: 打包最终代码
    Bundle-->>Dev: 运行时自动导入
```

## 🎯 核心设计模式

### 1. 依赖注入模式 (EventFlow)

```typescript
// 提供者模式
export function provideEventContext(handlers: Record<string, any>) {
  const refs = reactive<Record<string, any>>({})
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  return refs
}

// 消费者模式
export function useEventContext() {
  const refs = inject<Record<string, any>>(REFS_KEY)
  const handlers = inject<Record<string, any>>(HANDLERS_KEY)
  return { refs, handlers }
}
```

### 2. 策略模式 (API 协议)

```typescript
// 协议策略接口
interface ProtocolStrategy {
  buildParams(config: any, context: any): any
  processResponse(response: any, config: any): any
}

// REST 策略实现
class RestStrategy implements ProtocolStrategy {
  buildParams(config: ApiSchema, context: any) {
    return parseExpression(config.params || {}, context)
  }
}

// OData 策略实现
class ODataStrategy implements ProtocolStrategy {
  buildParams(config: ApiSchema, context: any) {
    return buildODataParams(config.odata || {}, context)
  }
}
```

### 3. 工厂模式 (组件创建)

```typescript
// 组件工厂
export function getComponentByName(type: string) {
  const componentMap = {
    'ne-button': NeButton,
    'ne-table': NeTable,
    'pro-table': NeProTable,
    'wg-basic-form': WidgetBasicForm,
    // ...
  }
  
  return componentMap[type] || 'div'
}
```

### 4. 观察者模式 (事件系统)

```typescript
// 事件发布订阅
export class EventBus {
  private events: Map<string, Function[]> = new Map()
  
  on(event: string, callback: Function) {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }
  
  emit(event: string, ...args: any[]) {
    const callbacks = this.events.get(event) || []
    callbacks.forEach(callback => callback(...args))
  }
}
```

## 🚀 性能优化策略

### 1. 组件懒加载

```typescript
// 动态导入组件
const LazyComponent = defineAsyncComponent(() => 
  import('./components/HeavyComponent.vue')
)

// 路由级别懒加载
const routes = [
  {
    path: '/heavy',
    component: () => import('./views/HeavyView.vue')
  }
]
```

### 2. 虚拟滚动

```typescript
// 大数据表格优化
export function useVirtualScroll(items: Ref<any[]>, itemHeight: number) {
  const containerHeight = ref(400)
  const scrollTop = ref(0)
  
  const visibleItems = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(
      start + Math.ceil(containerHeight.value / itemHeight) + 1,
      items.value.length
    )
    return items.value.slice(start, end)
  })
  
  return { visibleItems, scrollTop }
}
```

### 3. 响应式优化

```typescript
// 使用 shallowRef 优化大对象
const largeData = shallowRef({
  // 大量数据...
})

// 使用 markRaw 标记不需要响应式的对象
const staticConfig = markRaw({
  // 静态配置...
})
```

## 📊 监控和调试

### 1. 性能监控

```typescript
// 组件渲染性能监控
export function usePerformanceMonitor(componentName: string) {
  onMounted(() => {
    performance.mark(`${componentName}-mount-start`)
  })
  
  onUpdated(() => {
    performance.mark(`${componentName}-update`)
    performance.measure(
      `${componentName}-render-time`,
      `${componentName}-mount-start`,
      `${componentName}-update`
    )
  })
}
```

### 2. 错误边界

```typescript
// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  // 发送错误报告
  reportError(err, { instance, info })
}
```

### 3. 开发工具集成

```typescript
// Vue DevTools 集成
if (process.env.NODE_ENV === 'development') {
  app.config.globalProperties.$debug = {
    eventFlow: useEventFlow(),
    apiService: useApiService(),
    componentRegistry: getComponentRegistry()
  }
}
```

---

*技术架构文档 - 最后更新: 2025-01-21*
