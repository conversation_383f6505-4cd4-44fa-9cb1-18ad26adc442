# Neue CDP Space - Vue 3 低代码平台组件库

## 📋 项目概述

Neue CDP Space 是一个基于 Vue 3 的企业级低代码平台组件库，专为快速构建数据驱动的业务应用而设计。项目采用现代化的前端技术栈，提供了完整的组件系统、事件流机制、API 服务层和构建工具链。

### 🎯 核心特性

- **🚀 Vue 3 + TypeScript**: 基于最新的 Vue 3 Composition API 和 TypeScript
- **📦 Monorepo 架构**: 使用 pnpm workspace 管理多包项目
- **🎨 组件化设计**: 提供丰富的业务组件和基础组件
- **⚡ 事件流机制**: 创新的链式事件调用系统，支持低代码平台
- **🔌 插件化架构**: 支持组件自动导入和按需加载
- **🛠️ 完整工具链**: 包含构建、测试、代码规范等完整开发工具

## 🏗️ 项目架构

### 目录结构

```
neue-cdp-space/
├── packages/                    # 核心包目录
│   ├── components/             # 基础组件库
│   ├── widgets/               # 业务组件库
│   ├── neue-plus/             # 主包入口
│   ├── resolver/              # 组件解析器
│   ├── services/              # API 服务层
│   ├── hooks/                 # Vue 3 Hooks
│   ├── utils/                 # 工具函数
│   ├── constants/             # 常量定义
│   ├── directives/            # Vue 指令
│   ├── locale/                # 国际化
│   └── theme-chalk/           # 样式主题
├── internal/                   # 内部工具包
│   ├── build/                 # 构建工具
│   ├── build-utils/           # 构建工具函数
│   ├── build-constants/       # 构建常量
│   └── eslint-config/         # ESLint 配置
├── play/                      # 开发调试环境
├── scripts/                   # 构建脚本
└── docs/                      # 文档（待开发）
```

### 技术栈

**核心框架**

- Vue 3.2+ (Composition API)
- TypeScript 5.5+
- Element Plus 2.10+ (UI 基础)

**构建工具**

- Vite 5.x (开发服务器)
- Rollup (打包构建)
- Gulp (任务管理)
- pnpm 9.5+ (包管理)

**开发工具**

- Vitest (单元测试)
- ESLint + Prettier (代码规范)
- Husky + lint-staged (Git 钩子)
- Commitizen (提交规范)

## 🧩 核心模块

### 1. 组件系统 (Components)

#### 基础组件

- **NeButton**: 按钮组件
- **NeCard**: 卡片容器
- **NeTable**: 数据表格
- **NeProTable**: 高级表格（支持搜索、分页、排序）
- **NeTreeTable**: 树形表格
- **NeDrawer**: 抽屉组件
- **NeCheckboxGroup**: 复选框组合
- **NeRadioGroup**: 单选框组合
- **NeConfigProvider**: 全局配置提供者

#### 业务组件 (Widgets)

- **WidgetBasicForm**: 基础表单组件
- **WidgetBomTable**: BOM 表格组件
- **WidgetBomPanel**: BOM 面板组件

### 2. 事件流机制 (EventFlow)

创新的链式事件调用系统，支持低代码平台的动态行为控制：

```typescript
interface ActionNode {
  id: string
  type: 'start' | 'end' | 'normal'
  title: string
  config: {
    actionType: string // 行为类型
    target?: string // 目标组件
    delay?: number // 延迟时间
  }
}
```

**核心特性**：

- 基于 Vue 3 依赖注入的上下文管理
- 支持串行执行和延迟调用
- 组件方法动态绑定
- 链表结构的事件流控制

### 3. 渲染引擎 (MaterialRender)

低代码平台的核心渲染引擎，支持 JSON 配置驱动的页面生成：

```typescript
interface PageData {
  config: {
    configProvider?: Record<string, any>
    events?: ElementEvent[]
  }
  elements: SchemaElement[]
  apis: Record<string, ApiSchema>
}
```

### 4. API 服务层 (Services)

统一的 API 调用和数据处理层：

- **REST API 支持**: 标准 HTTP 请求
- **OData 协议支持**: 企业级数据查询
- **请求拦截器**: 统一错误处理和认证
- **数据映射**: 灵活的数据转换机制

## 🚀 快速开始

### 环境要求

- Node.js >= 20
- pnpm >= 9.5.0

### 安装依赖

```bash
# 安装项目依赖
pnpm install

# 构建所有包
pnpm build

# 启动开发环境
pnpm dev
```

### 开发调试

```bash
# 启动 playground 开发环境
pnpm dev

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 类型检查
pnpm typecheck
```

## 📦 包管理

### 主要包说明

| 包名                    | 版本   | 描述                     |
| ----------------------- | ------ | ------------------------ |
| `neue-plus`             | v0.0.1 | 主包，包含所有组件和功能 |
| `@neue-plus/components` | -      | 基础组件库               |
| `@neue-plus/widgets`    | -      | 业务组件库               |
| `@neue-plus/resolver`   | 1.0.0  | 组件自动导入解析器       |
| `@neue-plus/services`   | 0.0.5  | API 服务层               |

### 使用方式

```typescript
// 完整导入
import NeueePlus from 'neue-plus'
import 'neue-plus/dist/index.css'

app.use(NeueePlus)

// 按需导入
import { NeButton, NeTable } from 'neue-plus'

// 自动导入（推荐）
import Components from 'unplugin-vue-components/vite'
import { NeuePlusResolver } from '@neue-plus/resolver'

export default defineConfig({
  plugins: [
    Components({
      resolvers: [NeuePlusResolver()],
    }),
  ],
})
```

## 🛠️ 开发指南

### 新增组件

1. **创建组件目录**

```bash
packages/components/your-component/
├── src/
│   ├── your-component.vue
│   └── types.ts
├── style/
│   └── index.ts
└── index.ts
```

2. **注册组件**

```typescript
// packages/components/index.ts
export * from './your-component'

// packages/neue-plus/component.ts
import { YourComponent } from '@neue-plus/components'
export default [..., YourComponent] as Plugin[]
```

### 新增 Widget

1. **创建 Widget 目录**

```bash
packages/widgets/your-widget/
├── src/
│   ├── widget.vue
│   └── types.ts
└── index.ts
```

2. **使用 widget-wrapper 包裹**

```vue
<template>
  <widget-wrapper>
    <!-- 你的 Widget 内容 -->
  </widget-wrapper>
</template>
```

### 构建系统

项目使用 Gulp + Rollup 的混合构建系统：

- **模块构建**: 生成 ESM 和 CommonJS 格式
- **类型定义**: 自动生成 TypeScript 声明文件
- **样式处理**: Sass 编译和 CSS 优化
- **Bundle 构建**: UMD 格式的完整包

## 🧪 测试

### 测试配置

- **测试框架**: Vitest
- **测试环境**: jsdom
- **覆盖率**: v8 provider
- **测试工具**: @vue/test-utils

### 运行测试

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# SSR 测试
pnpm test:ssr
```

## 📝 代码规范

### ESLint 配置

项目使用自定义的 ESLint 配置 `@neue-plus/eslint-config`：

- 基于 `eslint:recommended`
- 支持 Vue 3 + TypeScript
- 集成 Prettier 格式化
- 包含 Unicorn 最佳实践

### 提交规范

使用 Conventional Commits 规范：

```bash
# 使用 commitizen
pnpm cz

# 提交格式
type(scope): description

# 示例
feat(components): add new button component
fix(widgets): resolve table rendering issue
```

## 🚢 部署发布

### 构建生产版本

```bash
# 清理构建产物
pnpm clean

# 构建所有包
pnpm build

# 构建主题样式
pnpm build:theme
```

### 发布配置

项目配置了私有 npm 仓库：

```json
{
  "publishConfig": {
    "registry": "http://*************:8081/repository/npm-hosted/"
  }
}
```

## 🔄 开发状态

### 已完成功能 ✅

**框架部分**

- 核心 UI 渲染器
- 事件流程规范与实现

**组件部分**

- Button、Card、Drawer 组件
- Table、ProTable、TreeTable 组件
- CheckboxGroup、RadioGroup 组件
- ConfigProvider 全局配置

### 待开发功能 🚧

**框架部分**

- 业务接口 API 封装
- OData API 封装
- 可视化设计器

**组件部分**

- 表单输入组件 (Input, InputNumber)
- 日期时间选择器
- 文件上传组件
- 树形控件和级联选择器
- 其他交互组件

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: https://github.com/neue-plus/neue-plus
- 官方网站: https://neue-plus.org/
- 问题反馈: https://github.com/neue-plus/neue-plus/issues

## 🔧 详细技术说明

### EventFlow 事件流机制详解

EventFlow 是本项目的核心创新，基于 Vue 3 的依赖注入系统实现：

#### 核心概念

1. **上下文提供 (provideEventContext)**

```typescript
export function provideEventContext(handlers: Record<string, any>) {
  const refs = reactive<Record<string, any>>({})
  provide(REFS_KEY, refs)
  provide(HANDLERS_KEY, handlers)
  return refs
}
```

2. **事件执行器 (useEventFlow)**

```typescript
export function useEventFlow() {
  const { refs, handlers } = useEventContext()

  function run(actions: ActionNode[] = [], params: Record<string, any> = {}) {
    const linkedList = toLinkedList(actions)
    if (linkedList) exec(linkedList, params)
  }

  return { run }
}
```

#### 使用示例

```typescript
// 定义事件流
const eventActions = [
  {
    config: {
      actionType: 'toggleExpandAll',
      target: 'TableComponent_1',
      delay: 300,
    },
  },
  {
    config: {
      actionType: 'showToast',
      delay: 100,
    },
  },
]

// 执行事件流
eventFlow.run(eventActions, { message: 'Hello World' })
```

### MaterialRender 渲染引擎

MaterialRender 是低代码平台的核心渲染引擎，支持通过 JSON 配置动态生成页面：

#### 配置结构

```typescript
interface MaterialRenderProps {
  config: {
    configProvider?: Record<string, any> // 全局配置
    events?: ElementEvent[] // 页面级事件
  }
  elements: SchemaElement[] // 组件树
  apis: Record<string, ApiSchema> // API 配置
}
```

#### 组件元素定义

```typescript
interface SchemaElement {
  id: string // 唯一标识
  type: string // 组件类型
  name?: string // 显示名称
  props?: Record<string, any> // 组件属性
  elements?: SchemaElement[] // 子组件
  events?: ElementEvent[] // 组件事件
}
```

#### 实际使用示例

```vue
<template>
  <NeMaterialRender v-bind="pageConfig" />
</template>

<script setup>
const pageConfig = {
  elements: [
    {
      id: 'table-1',
      type: 'pro-table',
      props: {
        columns: [
          { prop: 'name', label: '姓名' },
          { prop: 'age', label: '年龄' },
        ],
      },
      events: [
        {
          eventName: 'onRowClick',
          actions: [
            {
              config: {
                actionType: 'open',
                target: 'drawer-1',
              },
            },
          ],
        },
      ],
    },
  ],
  apis: {
    tableData: {
      url: '/api/users',
      method: 'get',
      protocol: 'rest',
    },
  },
}
</script>
```

### API 服务层详解

#### 支持的协议

1. **REST API**

```typescript
const restApi: ApiSchema = {
  url: '/api/users',
  method: 'get',
  headers: { Authorization: 'Bearer token' },
  params: { page: 1, size: 10 },
}
```

2. **OData 协议**

```typescript
const odataApi: ApiSchema = {
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  odata: {
    select: 'FirstName,LastName,Gender',
    filter: "Gender eq 'Male'",
    orderby: 'FirstName',
    top: 10,
    skip: 0,
  },
}
```

#### 数据处理

```typescript
// 执行 API 调用
const result = await executeApi(apiConfig, context)

// 数据路径提取
if (apiConfig.dataPath) {
  const data = getValueByPath(result, apiConfig.dataPath)
}

// 数据映射
if (apiConfig.map) {
  const mappedData = data.map((item) => ({
    label: item[apiConfig.map.label],
    value: item[apiConfig.map.value],
  }))
}
```

### 组件自动导入 (Resolver)

项目提供了 `@neue-plus/resolver` 包，支持 unplugin-vue-components 自动导入：

```typescript
// vite.config.ts
import Components from 'unplugin-vue-components/vite'
import { NeuePlusResolver } from '@neue-plus/resolver'

export default defineConfig({
  plugins: [
    Components({
      resolvers: [
        NeuePlusResolver({
          version: '1.0.0',
          importStyle: 'sass', // 'css' | 'sass' | false
        }),
      ],
    }),
  ],
})
```

### 主题系统

基于 Sass 的主题系统，支持深度定制：

#### 目录结构

```
packages/theme-chalk/src/
├── common/
│   ├── var.scss          # CSS 变量定义
│   └── transition.scss   # 过渡动画
├── mixins/
│   ├── mixins.scss       # 通用混入
│   └── utils.scss        # 工具函数
└── components/
    ├── button.scss       # 按钮样式
    ├── table.scss        # 表格样式
    └── ...
```

#### 自定义主题

```scss
// 覆盖默认变量
:root {
  --ne-color-primary: #409eff;
  --ne-color-success: #67c23a;
  --ne-color-warning: #e6a23c;
  --ne-color-danger: #f56c6c;
}

// 使用混入
@use '@neue-plus/theme-chalk/src/mixins/mixins' as *;

@include b(custom-component) {
  @include e(header) {
    color: getCssVar('color-primary');
  }
}
```

## 🎯 最佳实践

### 1. 组件开发

- 使用 `defineComponent` 和 Composition API
- 通过 `defineExpose` 暴露组件方法
- 使用 TypeScript 进行类型约束
- 遵循单一职责原则

### 2. 事件处理

- 优先使用 EventFlow 机制处理复杂交互
- 组件间通信通过事件流而非直接调用
- 合理使用延迟执行避免性能问题

### 3. API 设计

- 统一使用 services 层处理 API 调用
- 支持 OData 协议的企业级应用
- 实现请求缓存和错误重试机制

### 4. 性能优化

- 使用 `shallowRef` 和 `shallowReactive` 优化大数据渲染
- 合理使用 `v-memo` 缓存复杂计算
- 组件懒加载和代码分割

## 🔍 故障排除

### 常见问题

1. **组件自动导入失败**

   - 检查 resolver 配置是否正确
   - 确认组件名称符合命名规范
   - 验证 TypeScript 类型声明

2. **事件流执行异常**

   - 检查 target 组件是否正确注册
   - 确认 actionType 方法是否存在
   - 验证事件流配置格式

3. **API 调用失败**
   - 检查网络连接和 CORS 配置
   - 验证 API 配置参数
   - 查看控制台错误信息

### 调试技巧

```typescript
// 开启调试模式
const app = createApp(App)
app.config.globalProperties.$debug = true

// 事件流调试
eventFlow.run(actions, params, { debug: true })

// API 调试
executeApi(apiConfig, context, { verbose: true })
```

---

_最后更新: 2025-01-21_
