# Neue CDP Space PlantUML 流程图

## 1. 项目整体架构图

```plantuml
@startuml 项目整体架构
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle

package "应用层" as AppLayer {
  [低代码设计器] as Designer
  [业务应用] as BusinessApp
}

package "渲染层" as RenderLayer {
  [MaterialRender 渲染引擎] as MaterialRender
  [NeConfigProvider 全局配置] as ConfigProvider
}

package "组件层" as ComponentLayer {
  package "基础组件库" as BaseComponents {
    [NeButton] as Button
    [NeTable] as Table
    [NeProTable] as ProTable
    [NeDrawer] as Drawer
    [NeCard] as Card
  }

  package "业务组件库" as BusinessComponents {
    [WidgetBasicForm] as BasicForm
    [WidgetBomTable] as BomTable
    [WidgetBomPanel] as BomPanel
  }
}

package "服务层" as ServiceLayer {
  [EventFlow 事件引擎] as EventFlow
  [API 服务层] as ApiService
  [组件实例管理] as ComponentManager
}

package "协议层" as ProtocolLayer {
  [REST API] as RestApi
  [OData 协议] as ODataApi
}

package "工具层" as ToolLayer {
  [构建工具链] as BuildTools
  [组件解析器] as Resolver
  [主题系统] as ThemeSystem
}

package "基础层" as BaseLayer {
  [Vue 3 + TypeScript] as VueCore
  [Element Plus] as ElementPlus
  [pnpm Workspace] as PnpmWorkspace
}

' 连接关系
Designer --> MaterialRender
BusinessApp --> MaterialRender
MaterialRender --> ConfigProvider
MaterialRender --> BaseComponents
MaterialRender --> BusinessComponents
MaterialRender --> EventFlow
MaterialRender --> ApiService

BaseComponents --> ElementPlus
BusinessComponents --> BaseComponents

EventFlow --> ComponentManager
ApiService --> RestApi
ApiService --> ODataApi

BuildTools --> VueCore
Resolver --> BaseComponents
ThemeSystem --> BaseComponents

VueCore --> ElementPlus
@enduml
```

## 2. EventFlow 事件流程图

```plantuml
@startuml EventFlow事件流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "用户操作" as User
participant "组件实例" as Component
participant "EventFlow引擎" as EventFlow
participant "事件上下文" as Context
participant "目标组件" as TargetComponent
participant "全局处理器" as GlobalHandler

User -> Component: 触发事件 (click, submit等)
Component -> EventFlow: eventFlow.run(actions, params)

EventFlow -> EventFlow: 构建事件链表
note right: toLinkedList(actions)

loop 遍历事件链
  EventFlow -> EventFlow: 解析当前节点
  note right: 获取 actionType, target, delay

  alt 有目标组件
    EventFlow -> Context: 查找组件实例
    Context -> Context: refs[target]
    Context --> EventFlow: 返回组件实例
    EventFlow -> TargetComponent: 调用组件方法
    TargetComponent --> EventFlow: 执行结果
  else 全局处理器
    EventFlow -> GlobalHandler: 调用全局方法
    GlobalHandler --> EventFlow: 执行结果
  end

  alt 有延迟
    EventFlow -> EventFlow: setTimeout(delay)
  end

  EventFlow -> EventFlow: 移动到下一个节点
end

EventFlow --> Component: 事件流执行完成
Component --> User: 反馈结果
@enduml
```

## 3. MaterialRender 渲染流程图

```plantuml
@startuml MaterialRender渲染流程
!theme plain
skinparam backgroundColor #FFFFFF

start

:接收 JSON 配置;
note right: elements, apis, config

:解析全局配置;
:创建 ConfigProvider;

:初始化事件上下文;
note right: provideEventContext(handlers, apis)

:解析 API 配置;
:初始化 API 服务;

partition "递归渲染组件树" {
  :遍历 elements 数组;

  repeat
    :获取当前元素配置;
    note right: id, type, props, events, elements

    :根据 type 获取组件;
    note right: getComponentByName(type)

    if (是否有子元素?) then (yes)
      :递归渲染子元素;
    endif

    :绑定组件属性;
    :注册组件实例到 refs;
    :绑定事件处理器;

    :创建组件 VNode;

  repeat while (还有更多元素?)
}

:组合所有 VNode;
:返回渲染结果;

stop
@enduml
```

## 4. API 服务调用流程图

```plantuml
@startuml API服务调用流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "组件" as Component
participant "executeApi" as ExecuteApi
participant "表达式解析器" as ExpressionParser
participant "协议适配器" as ProtocolAdapter
participant "HTTP客户端" as HttpClient
participant "数据处理器" as DataProcessor

Component -> ExecuteApi: executeApi(apiSchema, context)

ExecuteApi -> ExpressionParser: 解析请求头
ExpressionParser --> ExecuteApi: 解析后的 headers

alt REST 协议
  ExecuteApi -> ExpressionParser: 解析查询参数
  ExpressionParser --> ExecuteApi: 解析后的 params
else OData 协议
  ExecuteApi -> ProtocolAdapter: buildODataParams(odata, context)
  ProtocolAdapter --> ExecuteApi: OData 查询参数
end

ExecuteApi -> ExpressionParser: 解析请求体
ExpressionParser --> ExecuteApi: 解析后的 body

ExecuteApi -> HttpClient: 发送 HTTP 请求
note right: axios.request(config)

HttpClient --> ExecuteApi: HTTP 响应

alt 有数据路径配置
  ExecuteApi -> DataProcessor: 提取数据路径
  DataProcessor --> ExecuteApi: 提取后的数据
end

alt 有数据映射配置
  ExecuteApi -> DataProcessor: 数据映射转换
  DataProcessor --> ExecuteApi: 映射后的数据
end

ExecuteApi --> Component: 返回处理后的数据

note over Component, DataProcessor
  支持错误处理和重试机制
  支持请求拦截器和响应拦截器
end note
@enduml
```

## 5. 组件开发生命周期图

```plantuml
@startuml 组件开发生命周期
!theme plain
skinparam backgroundColor #FFFFFF

start

:创建组件目录;
note right: packages/components/your-component/

:编写组件 Vue 文件;
note right: src/your-component.vue

:定义组件类型;
note right: src/types.ts

:编写样式文件;
note right: style/index.ts

:创建组件入口;
note right: index.ts

:注册到组件库;
note right: packages/components/index.ts

:添加到主包;
note right: packages/neue-plus/component.ts

:更新解析器;
note right: packages/resolver/

:编写单元测试;
note right: __tests__/

:运行测试;
if (测试通过?) then (yes)
  :构建组件;
  :发布到 npm;
  stop
else (no)
  :修复问题;
  :运行测试;
endif
@enduml
```

## 6. 构建系统流程图

```plantuml
@startuml 构建系统流程
!theme plain
skinparam backgroundColor #FFFFFF

start

:执行 pnpm build;

fork
  :清理构建产物;
  note right: clean task
fork again
  :创建输出目录;
  note right: createOutput task
end fork

fork
  :构建模块;
  note right: buildModules

  fork
    :构建 ESM 格式;
    note right: .mjs 文件
  fork again
    :构建 CommonJS 格式;
    note right: .js 文件
  end fork

fork again
  :构建完整包;
  note right: buildFullBundle

  fork
    :构建 UMD 格式;
    note right: 浏览器使用
  fork again
    :构建压缩版本;
    note right: .min.js
  end fork

fork again
  :生成类型定义;
  note right: generateTypesDefinitions

fork again
  :构建辅助工具;
  note right: buildHelper

fork again
  :构建主题样式;
  note right: buildThemeChalk
  :复制样式文件;
end fork

fork
  :复制类型定义;
fork again
  :复制其他文件;
end fork

:构建完成;
stop
@enduml
```

## 7. 组件自动导入流程图

```plantuml
@startuml 组件自动导入流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "开发者" as Developer
participant "Vite" as Vite
participant "unplugin-vue-components" as Plugin
participant "NeuePlusResolver" as Resolver
participant "组件注册表" as Registry

Developer -> Vite: 编写组件使用代码
note right: <NeButton />

Vite -> Plugin: 解析 SFC 模板

Plugin -> Plugin: 检测未导入的组件
note right: 发现 NeButton 未导入

Plugin -> Resolver: resolveComponent('NeButton')

Resolver -> Resolver: 匹配组件名称规则
note right: /^Ne[A-Z]/

alt 匹配成功
  Resolver -> Registry: 查找组件信息
  Registry --> Resolver: 返回组件路径和配置

  Resolver -> Resolver: 确定导入路径
  note right: 'neue-plus' 或具体包路径

  alt 需要样式
    Resolver -> Resolver: 确定样式路径
    note right: CSS 或 Sass 文件
  end

  Resolver --> Plugin: 返回导入配置
  note right: { from: 'neue-plus', name: 'NeButton', sideEffects: [...] }

  Plugin -> Plugin: 生成导入语句
  note right: import { NeButton } from 'neue-plus'

else 匹配失败
  Resolver --> Plugin: 返回 null
end

Plugin --> Vite: 返回转换后的代码

Vite -> Vite: 编译和打包

Vite --> Developer: 运行时自动可用
@enduml
```

## 8. Widget 业务组件架构图

```plantuml
@startuml Widget业务组件架构
!theme plain
skinparam backgroundColor #FFFFFF

package "Widget 容器层" {
  [WidgetContainer] as Container
  [WidgetWrapper] as Wrapper
}

package "业务 Widget" {
  [WidgetBasicForm] as BasicForm
  [WidgetBomTable] as BomTable
  [WidgetBomPanel] as BomPanel
  [WidgetCustom] as Custom
}

package "基础组件" {
  [NeTable] as Table
  [NeForm] as Form
  [NeDrawer] as Drawer
  [NeButton] as Button
}

package "数据服务" {
  [API Service] as ApiService
  [Data Adapter] as DataAdapter
}

package "事件系统" {
  [EventFlow] as EventFlow
  [Event Handlers] as Handlers
}

' Widget 组合关系
Container --> Wrapper
Wrapper --> BasicForm
Wrapper --> BomTable
Wrapper --> BomPanel
Wrapper --> Custom

' Widget 依赖基础组件
BasicForm --> Form
BasicForm --> Button
BomTable --> Table
BomTable --> Button
BomPanel --> Drawer
BomPanel --> Table

' 数据流
ApiService --> DataAdapter
DataAdapter --> BasicForm
DataAdapter --> BomTable
DataAdapter --> BomPanel

' 事件流
EventFlow --> Handlers
Handlers --> BasicForm
Handlers --> BomTable
Handlers --> BomPanel

note right of Container
  Widget 容器负责：
  - 统一的生命周期管理
  - 事件流上下文提供
  - 数据状态管理
end note

note right of Wrapper
  Widget 包装器负责：
  - 样式隔离
  - 错误边界
  - 性能监控
end note
@enduml
```

## 9. 数据流转图

```plantuml
@startuml 数据流转图
!theme plain
skinparam backgroundColor #FFFFFF

actor "用户" as User
participant "MaterialRender" as Render
participant "API Service" as ApiService
participant "Data Processor" as DataProcessor
participant "Component" as Component
participant "EventFlow" as EventFlow

User -> Render: 页面初始化

Render -> ApiService: 执行初始化 API
note right: 根据 apis 配置

ApiService -> ApiService: 解析 API 配置
ApiService -> ApiService: 发送 HTTP 请求

alt 请求成功
  ApiService -> DataProcessor: 处理响应数据
  DataProcessor -> DataProcessor: 数据路径提取
  DataProcessor -> DataProcessor: 数据映射转换
  DataProcessor --> ApiService: 处理后的数据
  ApiService --> Render: 返回数据

  Render -> Component: 传递数据到组件
  Component -> Component: 渲染数据
  Component --> User: 显示界面

else 请求失败
  ApiService --> Render: 返回错误信息
  Render -> Component: 传递错误状态
  Component --> User: 显示错误信息
end

User -> Component: 用户交互 (点击、输入等)
Component -> EventFlow: 触发事件流

EventFlow -> EventFlow: 执行事件链
EventFlow -> ApiService: 可能触发新的 API 调用
ApiService -> DataProcessor: 处理新数据
DataProcessor --> ApiService: 返回处理结果
ApiService --> EventFlow: 返回 API 结果
EventFlow -> Component: 更新组件状态
Component --> User: 更新界面

note over User, EventFlow
  数据流特点：
  - 单向数据流
  - 响应式更新
  - 错误处理机制
  - 缓存优化
end note
@enduml
```

## 7. 组件自动导入流程图

```plantuml
@startuml 组件自动导入流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "开发者" as Developer
participant "Vite" as Vite
participant "unplugin-vue-components" as Plugin
participant "NeuePlusResolver" as Resolver
participant "组件注册表" as Registry

Developer -> Vite: 编写组件使用代码
note right: <NeButton />

Vite -> Plugin: 解析 SFC 模板

Plugin -> Plugin: 检测未导入的组件
note right: 发现 NeButton 未导入

Plugin -> Resolver: resolveComponent('NeButton')

Resolver -> Resolver: 匹配组件名称规则
note right: /^Ne[A-Z]/

alt 匹配成功
  Resolver -> Registry: 查找组件信息
  Registry --> Resolver: 返回组件路径和配置

  Resolver -> Resolver: 确定导入路径
  note right: 'neue-plus' 或具体包路径

  alt 需要样式
    Resolver -> Resolver: 确定样式路径
    note right: CSS 或 Sass 文件
  end

  Resolver --> Plugin: 返回导入配置
  note right: { from: 'neue-plus', name: 'NeButton', sideEffects: [...] }

  Plugin -> Plugin: 生成导入语句
  note right: import { NeButton } from 'neue-plus'

else 匹配失败
  Resolver --> Plugin: 返回 null
end

Plugin --> Vite: 返回转换后的代码

Vite -> Vite: 编译和打包

Vite --> Developer: 运行时自动可用
@enduml
```

## 8. Widget 业务组件架构图

```plantuml
@startuml Widget业务组件架构
!theme plain
skinparam backgroundColor #FFFFFF

package "Widget 容器层" {
  [WidgetContainer] as Container
  [WidgetWrapper] as Wrapper
}

package "业务 Widget" {
  [WidgetBasicForm] as BasicForm
  [WidgetBomTable] as BomTable
  [WidgetBomPanel] as BomPanel
  [WidgetCustom] as Custom
}

package "基础组件" {
  [NeTable] as Table
  [NeForm] as Form
  [NeDrawer] as Drawer
  [NeButton] as Button
}

package "数据服务" {
  [API Service] as ApiService
  [Data Adapter] as DataAdapter
}

package "事件系统" {
  [EventFlow] as EventFlow
  [Event Handlers] as Handlers
}

' Widget 组合关系
Container --> Wrapper
Wrapper --> BasicForm
Wrapper --> BomTable
Wrapper --> BomPanel
Wrapper --> Custom

' Widget 依赖基础组件
BasicForm --> Form
BasicForm --> Button
BomTable --> Table
BomTable --> Button
BomPanel --> Drawer
BomPanel --> Table

' 数据流
ApiService --> DataAdapter
DataAdapter --> BasicForm
DataAdapter --> BomTable
DataAdapter --> BomPanel

' 事件流
EventFlow --> Handlers
Handlers --> BasicForm
Handlers --> BomTable
Handlers --> BomPanel

note right of Container
  Widget 容器负责：
  - 统一的生命周期管理
  - 事件流上下文提供
  - 数据状态管理
end note

note right of Wrapper
  Widget 包装器负责：
  - 样式隔离
  - 错误边界
  - 性能监控
end note
@enduml
```

## 9. 数据流转图

```plantuml
@startuml 数据流转图
!theme plain
skinparam backgroundColor #FFFFFF

actor "用户" as User
participant "MaterialRender" as Render
participant "API Service" as ApiService
participant "Data Processor" as DataProcessor
participant "Component" as Component
participant "EventFlow" as EventFlow

User -> Render: 页面初始化

Render -> ApiService: 执行初始化 API
note right: 根据 apis 配置

ApiService -> ApiService: 解析 API 配置
ApiService -> ApiService: 发送 HTTP 请求

alt 请求成功
  ApiService -> DataProcessor: 处理响应数据
  DataProcessor -> DataProcessor: 数据路径提取
  DataProcessor -> DataProcessor: 数据映射转换
  DataProcessor --> ApiService: 处理后的数据
  ApiService --> Render: 返回数据

  Render -> Component: 传递数据到组件
  Component -> Component: 渲染数据
  Component --> User: 显示界面

else 请求失败
  ApiService --> Render: 返回错误信息
  Render -> Component: 传递错误状态
  Component --> User: 显示错误信息
end

User -> Component: 用户交互 (点击、输入等)
Component -> EventFlow: 触发事件流

EventFlow -> EventFlow: 执行事件链
EventFlow -> ApiService: 可能触发新的 API 调用
ApiService -> DataProcessor: 处理新数据
DataProcessor --> ApiService: 返回处理结果
ApiService --> EventFlow: 返回 API 结果
EventFlow -> Component: 更新组件状态
Component --> User: 更新界面

note over User, EventFlow
  数据流特点：
  - 单向数据流
  - 响应式更新
  - 错误处理机制
  - 缓存优化
end note
@enduml
```

## 10. 主题系统架构图

```plantuml
@startuml 主题系统架构
!theme plain
skinparam backgroundColor #FFFFFF

package "主题配置层" {
  [CSS 变量定义] as CssVars
  [Sass 变量] as SassVars
  [主题配置文件] as ThemeConfig
}

package "样式构建层" {
  [Sass 编译器] as SassCompiler
  [PostCSS 处理器] as PostCSS
  [样式优化器] as StyleOptimizer
}

package "组件样式层" {
  [基础组件样式] as BaseStyles
  [业务组件样式] as WidgetStyles
  [全局样式] as GlobalStyles
}

package "工具函数层" {
  [BEM 混入] as BemMixins
  [响应式混入] as ResponsiveMixins
  [工具类混入] as UtilityMixins
}

package "运行时层" {
  [useNamespace Hook] as NamespaceHook
  [动态主题切换] as ThemeSwitcher
  [CSS-in-JS 支持] as CssInJs
}

' 配置流向
ThemeConfig --> CssVars
ThemeConfig --> SassVars

' 构建流向
SassVars --> SassCompiler
CssVars --> SassCompiler
SassCompiler --> PostCSS
PostCSS --> StyleOptimizer

' 样式依赖
BemMixins --> BaseStyles
ResponsiveMixins --> BaseStyles
UtilityMixins --> BaseStyles
BaseStyles --> WidgetStyles
GlobalStyles --> BaseStyles

' 运行时使用
NamespaceHook --> BaseStyles
ThemeSwitcher --> CssVars
CssInJs --> BaseStyles

note right of ThemeConfig
  主题配置包含：
  - 颜色系统
  - 字体系统
  - 间距系统
  - 断点系统
end note

note right of BemMixins
  BEM 混入提供：
  - @include b(block)
  - @include e(element)
  - @include m(modifier)
  - @include when(state)
end note
@enduml
```

## 11. 测试架构图

```plantuml
@startuml 测试架构图
!theme plain
skinparam backgroundColor #FFFFFF

package "测试框架层" {
  [Vitest] as Vitest
  [Vue Test Utils] as VueTestUtils
  [jsdom] as Jsdom
  [Happy DOM] as HappyDom
}

package "测试类型层" {
  [单元测试] as UnitTest
  [集成测试] as IntegrationTest
  [组件测试] as ComponentTest
  [E2E 测试] as E2eTest
}

package "测试工具层" {
  [Mock 工具] as MockTools
  [测试数据生成] as TestDataGen
  [断言库] as AssertionLib
  [覆盖率工具] as CoverageTools
}

package "测试配置层" {
  [Vitest 配置] as VitestConfig
  [测试环境配置] as TestEnvConfig
  [Mock 配置] as MockConfig
}

package "CI/CD 集成" {
  [GitHub Actions] as GithubActions
  [测试报告] as TestReports
  [覆盖率报告] as CoverageReports
}

' 框架依赖
UnitTest --> Vitest
ComponentTest --> VueTestUtils
IntegrationTest --> Jsdom
E2eTest --> HappyDom

' 工具使用
UnitTest --> MockTools
ComponentTest --> TestDataGen
UnitTest --> AssertionLib
ComponentTest --> AssertionLib

' 配置关系
Vitest --> VitestConfig
MockTools --> MockConfig
Jsdom --> TestEnvConfig

' CI/CD 流程
GithubActions --> UnitTest
GithubActions --> ComponentTest
GithubActions --> IntegrationTest
UnitTest --> TestReports
ComponentTest --> CoverageReports
CoverageTools --> CoverageReports

note right of UnitTest
  单元测试覆盖：
  - 工具函数
  - Hooks
  - 组件逻辑
  - API 服务
end note

note right of ComponentTest
  组件测试覆盖：
  - 组件渲染
  - 事件处理
  - Props 传递
  - 插槽内容
end note
@enduml
```

## 12. 低代码平台完整流程图

```plantuml
@startuml 低代码平台完整流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "设计师" as Designer
actor "开发者" as Developer
actor "最终用户" as EndUser

box "设计阶段" #LightBlue
  participant "可视化设计器" as VisualDesigner
  participant "组件库面板" as ComponentPanel
  participant "属性配置面板" as PropertyPanel
  participant "事件配置面板" as EventPanel
end box

box "配置生成" #LightGreen
  participant "JSON 配置生成器" as JsonGenerator
  participant "配置验证器" as ConfigValidator
  participant "配置存储" as ConfigStorage
end box

box "运行时渲染" #LightYellow
  participant "MaterialRender" as MaterialRender
  participant "组件注册表" as ComponentRegistry
  participant "事件流引擎" as EventFlowEngine
  participant "API 服务" as ApiService
end box

box "用户交互" #LightPink
  participant "渲染页面" as RenderedPage
  participant "用户界面" as UserInterface
end box

== 设计阶段 ==
Designer -> VisualDesigner: 打开设计器
VisualDesigner -> ComponentPanel: 显示可用组件
Designer -> ComponentPanel: 拖拽组件到画布
ComponentPanel -> PropertyPanel: 显示组件属性
Designer -> PropertyPanel: 配置组件属性
Designer -> EventPanel: 配置组件事件
EventPanel -> EventPanel: 设计事件流

== 配置生成 ==
VisualDesigner -> JsonGenerator: 生成页面配置
JsonGenerator -> ConfigValidator: 验证配置格式
ConfigValidator -> ConfigStorage: 存储有效配置

== 开发集成 ==
Developer -> ConfigStorage: 获取页面配置
ConfigStorage --> Developer: 返回 JSON 配置

== 运行时渲染 ==
Developer -> MaterialRender: 传入页面配置
MaterialRender -> ComponentRegistry: 查找组件定义
ComponentRegistry --> MaterialRender: 返回组件实例
MaterialRender -> EventFlowEngine: 初始化事件流
MaterialRender -> ApiService: 初始化数据服务
MaterialRender -> RenderedPage: 渲染页面

== 用户交互 ==
EndUser -> UserInterface: 访问页面
UserInterface -> RenderedPage: 显示渲染结果
EndUser -> RenderedPage: 用户操作
RenderedPage -> EventFlowEngine: 触发事件流
EventFlowEngine -> ApiService: 调用 API
ApiService --> EventFlowEngine: 返回数据
EventFlowEngine -> RenderedPage: 更新页面
RenderedPage --> EndUser: 显示更新结果

note over Designer, EndUser
  低代码平台特点：
  - 可视化设计
  - 配置驱动
  - 事件流控制
  - 实时预览
  - 一键部署
end note
@enduml
```

## 13. 包依赖关系图

```plantuml
@startuml 包依赖关系图
!theme plain
skinparam backgroundColor #FFFFFF

package "主包" {
  [neue-plus] as MainPackage
}

package "核心包" {
  [components] as Components
  [widgets] as Widgets
  [hooks] as Hooks
  [utils] as Utils
  [constants] as Constants
  [directives] as Directives
  [locale] as Locale
  [theme-chalk] as ThemeChalk
  [services] as Services
}

package "工具包" {
  [resolver] as Resolver
  [build] as Build
  [build-utils] as BuildUtils
  [build-constants] as BuildConstants
  [eslint-config] as EslintConfig
}

package "外部依赖" {
  [Vue 3] as Vue3
  [TypeScript] as TypeScript
  [Element Plus] as ElementPlus
  [Vite] as Vite
  [Rollup] as Rollup
}

' 主包依赖
MainPackage --> Components
MainPackage --> Widgets
MainPackage --> Hooks
MainPackage --> Utils
MainPackage --> Constants
MainPackage --> Directives
MainPackage --> Locale
MainPackage --> ThemeChalk

' 核心包内部依赖
Components --> Hooks
Components --> Utils
Components --> Constants
Components --> ThemeChalk
Widgets --> Components
Widgets --> Hooks
Services --> Utils

' 工具包依赖
Resolver --> MainPackage
Build --> BuildUtils
Build --> BuildConstants
BuildUtils --> Utils

' 外部依赖
Components --> Vue3
Components --> ElementPlus
MainPackage --> TypeScript
Build --> Vite
Build --> Rollup

note right of MainPackage
  主包作为统一入口
  导出所有子包功能
end note

note right of Components
  基础组件库
  依赖 Element Plus
end note

note right of Widgets
  业务组件库
  基于基础组件构建
end note
@enduml
```

## 14. 性能优化策略图

```plantuml
@startuml 性能优化策略图
!theme plain
skinparam backgroundColor #FFFFFF

package "构建时优化" {
  [Tree Shaking] as TreeShaking
  [代码分割] as CodeSplitting
  [Bundle 分析] as BundleAnalysis
  [压缩优化] as Compression
}

package "运行时优化" {
  [组件懒加载] as LazyLoading
  [虚拟滚动] as VirtualScroll
  [响应式优化] as ReactivityOpt
  [缓存策略] as CacheStrategy
}

package "渲染优化" {
  [v-memo 缓存] as VMemo
  [shallowRef 优化] as ShallowRef
  [异步组件] as AsyncComponent
  [Keep-alive 缓存] as KeepAlive
}

package "网络优化" {
  [API 缓存] as ApiCache
  [请求合并] as RequestMerge
  [预加载策略] as Preloading
  [CDN 加速] as CdnAcceleration
}

package "监控分析" {
  [性能监控] as PerformanceMonitor
  [错误追踪] as ErrorTracking
  [用户体验分析] as UxAnalysis
  [资源使用分析] as ResourceAnalysis
}

' 优化策略关系
TreeShaking --> CodeSplitting
CodeSplitting --> LazyLoading
LazyLoading --> AsyncComponent

VirtualScroll --> ReactivityOpt
ReactivityOpt --> ShallowRef
ShallowRef --> VMemo

ApiCache --> RequestMerge
RequestMerge --> Preloading
Preloading --> CdnAcceleration

PerformanceMonitor --> ErrorTracking
ErrorTracking --> UxAnalysis
UxAnalysis --> ResourceAnalysis

' 监控反馈
ResourceAnalysis --> TreeShaking
UxAnalysis --> VirtualScroll
PerformanceMonitor --> ApiCache

note right of TreeShaking
  构建时移除未使用代码
  减少包体积
end note

note right of VirtualScroll
  大数据列表优化
  只渲染可见区域
end note

note right of ApiCache
  智能缓存策略
  减少重复请求
end note
@enduml
```

## 15. 项目开发工作流程图

```plantuml
@startuml 项目开发工作流程
!theme plain
skinparam backgroundColor #FFFFFF

|开发者|
start
:克隆项目;
:安装依赖 (pnpm install);
:启动开发环境 (pnpm dev);

|功能开发|
:创建功能分支;
if (开发类型?) then (组件)
  :创建组件目录;
  :编写组件代码;
  :编写组件样式;
  :编写组件测试;
elseif (Widget)
  :创建 Widget 目录;
  :编写 Widget 代码;
  :使用 WidgetWrapper 包装;
  :编写 Widget 测试;
elseif (API 服务)
  :编写 API 配置;
  :实现数据处理逻辑;
  :编写服务测试;
else (其他)
  :编写相关代码;
  :编写测试用例;
endif

|代码质量|
:运行 ESLint 检查;
:运行 Prettier 格式化;
:运行 TypeScript 检查;
:运行单元测试;

if (所有检查通过?) then (是)
  |版本控制|
  :提交代码 (使用 commitizen);
  :推送到远程分支;
  :创建 Pull Request;
else (否)
  :修复问题;
  :重新检查;
endif

|代码审查|
:团队成员审查代码;
if (审查通过?) then (是)
  :合并到主分支;
else (否)
  :根据反馈修改;
  :重新提交;
endif

|持续集成|
:触发 CI/CD 流程;
:运行自动化测试;
:构建项目;
:生成构建产物;

if (CI/CD 成功?) then (是)
  |发布部署|
  :更新版本号;
  :发布到 npm 仓库;
  :部署到测试环境;
  :通知相关人员;
  stop
else (否)
  :查看构建日志;
  :修复构建问题;
  :重新触发构建;
endif

note right
  开发工作流特点：
  - 标准化的开发流程
  - 自动化的代码检查
  - 完整的测试覆盖
  - 持续集成部署
  - 团队协作规范
end note
@enduml
```

## 15. 项目开发工作流程图

```plantuml
@startuml 项目开发工作流程
!theme plain
skinparam backgroundColor #FFFFFF

|开发者|
start
:克隆项目;
:安装依赖 (pnpm install);
:启动开发环境 (pnpm dev);

|功能开发|
:创建功能分支;
if (开发类型?) then (组件)
  :创建组件目录;
  :编写组件代码;
  :编写组件样式;
  :编写组件测试;
elseif (Widget)
  :创建 Widget 目录;
  :编写 Widget 代码;
  :使用 WidgetWrapper 包装;
  :编写 Widget 测试;
elseif (API 服务)
  :编写 API 配置;
  :实现数据处理逻辑;
  :编写服务测试;
else (其他)
  :编写相关代码;
  :编写测试用例;
endif

|代码质量|
:运行 ESLint 检查;
:运行 Prettier 格式化;
:运行 TypeScript 检查;
:运行单元测试;

if (所有检查通过?) then (是)
  |版本控制|
  :提交代码 (使用 commitizen);
  :推送到远程分支;
  :创建 Pull Request;
else (否)
  :修复问题;
  :重新检查;
endif

|代码审查|
:团队成员审查代码;
if (审查通过?) then (是)
  :合并到主分支;
else (否)
  :根据反馈修改;
  :重新提交;
endif

|持续集成|
:触发 CI/CD 流程;
:运行自动化测试;
:构建项目;
:生成构建产物;

if (CI/CD 成功?) then (是)
  |发布部署|
  :更新版本号;
  :发布到 npm 仓库;
  :部署到测试环境;
  :通知相关人员;
  stop
else (否)
  :查看构建日志;
  :修复构建问题;
  :重新触发构建;
endif

note right
  开发工作流特点：
  - 标准化的开发流程
  - 自动化的代码检查
  - 完整的测试覆盖
  - 持续集成部署
  - 团队协作规范
end note
@enduml
```

---

## 📊 PlantUML 图表总结

本文档包含了 Neue CDP Space 项目的 15 个核心流程图：

### 🏗️ 架构设计类图表

1. **项目整体架构图** - 展示项目的分层架构和组件关系
2. **包依赖关系图** - 展示项目各包之间的依赖关系
3. **Widget 业务组件架构图** - 说明业务组件的架构设计
4. **主题系统架构图** - 说明主题系统的设计和实现
5. **测试架构图** - 展示测试体系的完整架构

### 🔄 流程机制类图表

6. **EventFlow 事件流程图** - 详细说明事件流的执行机制
7. **MaterialRender 渲染流程图** - 展示低代码渲染引擎的工作流程
8. **API 服务调用流程图** - 说明 API 服务的调用和数据处理过程
9. **数据流转图** - 展示数据在系统中的流转过程
10. **组件自动导入流程图** - 展示组件自动导入的实现机制

### 🛠️ 开发工作类图表

11. **组件开发生命周期图** - 展示组件从创建到发布的完整流程
12. **构建系统流程图** - 说明项目的构建和打包过程
13. **项目开发工作流程图** - 展示标准的开发工作流程

### 🎯 业务应用类图表

14. **低代码平台完整流程图** - 从设计到运行的完整流程
15. **性能优化策略图** - 说明项目的性能优化方案

### 📋 图表使用说明

#### 如何使用这些图表

1. **学习项目架构**: 通过架构设计类图表了解项目整体设计
2. **理解核心机制**: 通过流程机制类图表掌握核心技术实现
3. **指导开发工作**: 通过开发工作类图表规范开发流程
4. **业务场景应用**: 通过业务应用类图表了解实际使用场景

#### 图表渲染方式

这些 PlantUML 图表可以通过以下方式渲染：

1. **在线渲染**: 使用 [PlantUML Online Server](http://www.plantuml.com/plantuml/uml/)
2. **VS Code 插件**: 安装 PlantUML 插件进行本地渲染
3. **命令行工具**: 使用 PlantUML JAR 包进行批量渲染
4. **集成到文档**: 在 Markdown 文档中直接嵌入渲染结果

#### 图表维护建议

- 随着项目演进及时更新图表内容
- 保持图表的简洁性和可读性
- 为复杂图表添加详细的说明文档
- 定期检查图表与实际代码的一致性

### 🎨 图表特色

- **全面覆盖**: 涵盖项目的所有核心方面
- **层次清晰**: 从整体架构到具体实现的多层次展示
- **实用导向**: 每个图表都有明确的使用场景和指导意义
- **标准规范**: 使用统一的 PlantUML 语法和样式
- **易于维护**: 文本格式便于版本控制和协作维护

这些图表为 Neue CDP Space 项目提供了完整的可视化技术文档，帮助开发者快速理解项目架构、掌握核心技术、规范开发流程，是项目开发和维护的重要参考资料。

---

_PlantUML 流程图文档 - 最后更新: 2025-01-21_

1. **项目整体架构图** - 展示项目的分层架构和组件关系
2. **EventFlow 事件流程图** - 详细说明事件流的执行机制
3. **MaterialRender 渲染流程图** - 展示低代码渲染引擎的工作流程
4. **API 服务调用流程图** - 说明 API 服务的调用和数据处理过程
5. **组件开发生命周期图** - 展示组件从创建到发布的完整流程
6. **构建系统流程图** - 说明项目的构建和打包过程
7. **组件自动导入流程图** - 展示组件自动导入的实现机制
8. **Widget 业务组件架构图** - 说明业务组件的架构设计
9. **数据流转图** - 展示数据在系统中的流转过程
10. **主题系统架构图** - 说明主题系统的设计和实现
11. **测试架构图** - 展示测试体系的完整架构
12. **低代码平台完整流程图** - 从设计到运行的完整流程
13. **包依赖关系图** - 展示项目各包之间的依赖关系
14. **性能优化策略图** - 说明项目的性能优化方案
15. **项目开发工作流程图** - 展示标准的开发工作流程

这些图表全面覆盖了项目的架构设计、开发流程、技术实现等各个方面，为开发者提供了清晰的技术指导和工作流程参考。
