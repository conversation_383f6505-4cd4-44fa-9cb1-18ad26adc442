# Neue CDP Space PlantUML 流程图

## 1. 项目整体架构图

```plantuml
@startuml 项目整体架构
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle

package "应用层" as AppLayer {
  [低代码设计器] as Designer
  [业务应用] as BusinessApp
}

package "渲染层" as RenderLayer {
  [MaterialRender 渲染引擎] as MaterialRender
  [NeConfigProvider 全局配置] as ConfigProvider
}

package "组件层" as ComponentLayer {
  package "基础组件库" as BaseComponents {
    [NeButton] as Button
    [NeTable] as Table
    [NeProTable] as ProTable
    [NeDrawer] as Drawer
    [NeCard] as Card
  }

  package "业务组件库" as BusinessComponents {
    [WidgetBasicForm] as BasicForm
    [WidgetBomTable] as BomTable
    [WidgetBomPanel] as BomPanel
  }
}

package "服务层" as ServiceLayer {
  [EventFlow 事件引擎] as EventFlow
  [API 服务层] as ApiService
  [组件实例管理] as ComponentManager
}

package "协议层" as ProtocolLayer {
  [REST API] as RestApi
  [OData 协议] as ODataApi
}

package "工具层" as ToolLayer {
  [构建工具链] as BuildTools
  [组件解析器] as Resolver
  [主题系统] as ThemeSystem
}

package "基础层" as BaseLayer {
  [Vue 3 + TypeScript] as VueCore
  [Element Plus] as ElementPlus
  [pnpm Workspace] as PnpmWorkspace
}

' 连接关系
Designer --> MaterialRender
BusinessApp --> MaterialRender
MaterialRender --> ConfigProvider
MaterialRender --> BaseComponents
MaterialRender --> BusinessComponents
MaterialRender --> EventFlow
MaterialRender --> ApiService

BaseComponents --> ElementPlus
BusinessComponents --> BaseComponents

EventFlow --> ComponentManager
ApiService --> RestApi
ApiService --> ODataApi

BuildTools --> VueCore
Resolver --> BaseComponents
ThemeSystem --> BaseComponents

VueCore --> ElementPlus
@enduml
```

## 2. EventFlow 事件流程图

```plantuml
@startuml EventFlow事件流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "用户操作" as User
participant "组件实例" as Component
participant "EventFlow引擎" as EventFlow
participant "事件上下文" as Context
participant "目标组件" as TargetComponent
participant "全局处理器" as GlobalHandler

User -> Component: 触发事件 (click, submit等)
Component -> EventFlow: eventFlow.run(actions, params)

EventFlow -> EventFlow: 构建事件链表
note right: toLinkedList(actions)

loop 遍历事件链
  EventFlow -> EventFlow: 解析当前节点
  note right: 获取 actionType, target, delay

  alt 有目标组件
    EventFlow -> Context: 查找组件实例
    Context -> Context: refs[target]
    Context --> EventFlow: 返回组件实例
    EventFlow -> TargetComponent: 调用组件方法
    TargetComponent --> EventFlow: 执行结果
  else 全局处理器
    EventFlow -> GlobalHandler: 调用全局方法
    GlobalHandler --> EventFlow: 执行结果
  end

  alt 有延迟
    EventFlow -> EventFlow: setTimeout(delay)
  end

  EventFlow -> EventFlow: 移动到下一个节点
end

EventFlow --> Component: 事件流执行完成
Component --> User: 反馈结果
@enduml
```

## 3. MaterialRender 渲染流程图

```plantuml
@startuml MaterialRender渲染流程
!theme plain
skinparam backgroundColor #FFFFFF

start

:接收 JSON 配置;
note right: elements, apis, config

:解析全局配置;
:创建 ConfigProvider;

:初始化事件上下文;
note right: provideEventContext(handlers, apis)

:解析 API 配置;
:初始化 API 服务;

partition "递归渲染组件树" {
  :遍历 elements 数组;

  repeat
    :获取当前元素配置;
    note right: id, type, props, events, elements

    :根据 type 获取组件;
    note right: getComponentByName(type)

    if (是否有子元素?) then (yes)
      :递归渲染子元素;
    endif

    :绑定组件属性;
    :注册组件实例到 refs;
    :绑定事件处理器;

    :创建组件 VNode;

  repeat while (还有更多元素?)
}

:组合所有 VNode;
:返回渲染结果;

stop
@enduml
```

## 4. API 服务调用流程图

```plantuml
@startuml API服务调用流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "组件" as Component
participant "executeApi" as ExecuteApi
participant "表达式解析器" as ExpressionParser
participant "协议适配器" as ProtocolAdapter
participant "HTTP客户端" as HttpClient
participant "数据处理器" as DataProcessor

Component -> ExecuteApi: executeApi(apiSchema, context)

ExecuteApi -> ExpressionParser: 解析请求头
ExpressionParser --> ExecuteApi: 解析后的 headers

alt REST 协议
  ExecuteApi -> ExpressionParser: 解析查询参数
  ExpressionParser --> ExecuteApi: 解析后的 params
else OData 协议
  ExecuteApi -> ProtocolAdapter: buildODataParams(odata, context)
  ProtocolAdapter --> ExecuteApi: OData 查询参数
end

ExecuteApi -> ExpressionParser: 解析请求体
ExpressionParser --> ExecuteApi: 解析后的 body

ExecuteApi -> HttpClient: 发送 HTTP 请求
note right: axios.request(config)

HttpClient --> ExecuteApi: HTTP 响应

alt 有数据路径配置
  ExecuteApi -> DataProcessor: 提取数据路径
  DataProcessor --> ExecuteApi: 提取后的数据
end

alt 有数据映射配置
  ExecuteApi -> DataProcessor: 数据映射转换
  DataProcessor --> ExecuteApi: 映射后的数据
end

ExecuteApi --> Component: 返回处理后的数据

note over Component, DataProcessor
  支持错误处理和重试机制
  支持请求拦截器和响应拦截器
end note
@enduml
```

## 5. 组件开发生命周期图

```plantuml
@startuml 组件开发生命周期
!theme plain
skinparam backgroundColor #FFFFFF

start

:创建组件目录;
note right: packages/components/your-component/

:编写组件 Vue 文件;
note right: src/your-component.vue

:定义组件类型;
note right: src/types.ts

:编写样式文件;
note right: style/index.ts

:创建组件入口;
note right: index.ts

:注册到组件库;
note right: packages/components/index.ts

:添加到主包;
note right: packages/neue-plus/component.ts

:更新解析器;
note right: packages/resolver/

:编写单元测试;
note right: __tests__/

:运行测试;
if (测试通过?) then (yes)
  :构建组件;
  :发布到 npm;
  stop
else (no)
  :修复问题;
  :运行测试;
endif
@enduml
```

## 6. 构建系统流程图

```plantuml
@startuml 构建系统流程
!theme plain
skinparam backgroundColor #FFFFFF

start

:执行 pnpm build;

fork
  :清理构建产物;
  note right: clean task
fork again
  :创建输出目录;
  note right: createOutput task
end fork

fork
  :构建模块;
  note right: buildModules

  fork
    :构建 ESM 格式;
    note right: .mjs 文件
  fork again
    :构建 CommonJS 格式;
    note right: .js 文件
  end fork

fork again
  :构建完整包;
  note right: buildFullBundle

  fork
    :构建 UMD 格式;
    note right: 浏览器使用
  fork again
    :构建压缩版本;
    note right: .min.js
  end fork

fork again
  :生成类型定义;
  note right: generateTypesDefinitions

fork again
  :构建辅助工具;
  note right: buildHelper

fork again
  :构建主题样式;
  note right: buildThemeChalk
  :复制样式文件;
end fork

fork
  :复制类型定义;
fork again
  :复制其他文件;
end fork

:构建完成;
stop
@enduml
```

## 7. 组件自动导入流程图

```plantuml
@startuml 组件自动导入流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "开发者" as Developer
participant "Vite" as Vite
participant "unplugin-vue-components" as Plugin
participant "NeuePlusResolver" as Resolver
participant "组件注册表" as Registry

Developer -> Vite: 编写组件使用代码
note right: <NeButton />

Vite -> Plugin: 解析 SFC 模板

Plugin -> Plugin: 检测未导入的组件
note right: 发现 NeButton 未导入

Plugin -> Resolver: resolveComponent('NeButton')

Resolver -> Resolver: 匹配组件名称规则
note right: /^Ne[A-Z]/

alt 匹配成功
  Resolver -> Registry: 查找组件信息
  Registry --> Resolver: 返回组件路径和配置

  Resolver -> Resolver: 确定导入路径
  note right: 'neue-plus' 或具体包路径

  alt 需要样式
    Resolver -> Resolver: 确定样式路径
    note right: CSS 或 Sass 文件
  end

  Resolver --> Plugin: 返回导入配置
  note right: { from: 'neue-plus', name: 'NeButton', sideEffects: [...] }

  Plugin -> Plugin: 生成导入语句
  note right: import { NeButton } from 'neue-plus'

else 匹配失败
  Resolver --> Plugin: 返回 null
end

Plugin --> Vite: 返回转换后的代码

Vite -> Vite: 编译和打包

Vite --> Developer: 运行时自动可用
@enduml
```

## 8. Widget 业务组件架构图

```plantuml
@startuml Widget业务组件架构
!theme plain
skinparam backgroundColor #FFFFFF

package "Widget 容器层" {
  [WidgetContainer] as Container
  [WidgetWrapper] as Wrapper
}

package "业务 Widget" {
  [WidgetBasicForm] as BasicForm
  [WidgetBomTable] as BomTable
  [WidgetBomPanel] as BomPanel
  [WidgetCustom] as Custom
}

package "基础组件" {
  [NeTable] as Table
  [NeForm] as Form
  [NeDrawer] as Drawer
  [NeButton] as Button
}

package "数据服务" {
  [API Service] as ApiService
  [Data Adapter] as DataAdapter
}

package "事件系统" {
  [EventFlow] as EventFlow
  [Event Handlers] as Handlers
}

' Widget 组合关系
Container --> Wrapper
Wrapper --> BasicForm
Wrapper --> BomTable
Wrapper --> BomPanel
Wrapper --> Custom

' Widget 依赖基础组件
BasicForm --> Form
BasicForm --> Button
BomTable --> Table
BomTable --> Button
BomPanel --> Drawer
BomPanel --> Table

' 数据流
ApiService --> DataAdapter
DataAdapter --> BasicForm
DataAdapter --> BomTable
DataAdapter --> BomPanel

' 事件流
EventFlow --> Handlers
Handlers --> BasicForm
Handlers --> BomTable
Handlers --> BomPanel

note right of Container
  Widget 容器负责：
  - 统一的生命周期管理
  - 事件流上下文提供
  - 数据状态管理
end note

note right of Wrapper
  Widget 包装器负责：
  - 样式隔离
  - 错误边界
  - 性能监控
end note
@enduml
```

## 9. 数据流转图

```plantuml
@startuml 数据流转图
!theme plain
skinparam backgroundColor #FFFFFF

actor "用户" as User
participant "MaterialRender" as Render
participant "API Service" as ApiService
participant "Data Processor" as DataProcessor
participant "Component" as Component
participant "EventFlow" as EventFlow

User -> Render: 页面初始化

Render -> ApiService: 执行初始化 API
note right: 根据 apis 配置

ApiService -> ApiService: 解析 API 配置
ApiService -> ApiService: 发送 HTTP 请求

alt 请求成功
  ApiService -> DataProcessor: 处理响应数据
  DataProcessor -> DataProcessor: 数据路径提取
  DataProcessor -> DataProcessor: 数据映射转换
  DataProcessor --> ApiService: 处理后的数据
  ApiService --> Render: 返回数据

  Render -> Component: 传递数据到组件
  Component -> Component: 渲染数据
  Component --> User: 显示界面

else 请求失败
  ApiService --> Render: 返回错误信息
  Render -> Component: 传递错误状态
  Component --> User: 显示错误信息
end

User -> Component: 用户交互 (点击、输入等)
Component -> EventFlow: 触发事件流

EventFlow -> EventFlow: 执行事件链
EventFlow -> ApiService: 可能触发新的 API 调用
ApiService -> DataProcessor: 处理新数据
DataProcessor --> ApiService: 返回处理结果
ApiService --> EventFlow: 返回 API 结果
EventFlow -> Component: 更新组件状态
Component --> User: 更新界面

note over User, EventFlow
  数据流特点：
  - 单向数据流
  - 响应式更新
  - 错误处理机制
  - 缓存优化
end note
@enduml
```

## 7. 组件自动导入流程图

```plantuml
@startuml 组件自动导入流程
!theme plain
skinparam backgroundColor #FFFFFF

actor "开发者" as Developer
participant "Vite" as Vite
participant "unplugin-vue-components" as Plugin
participant "NeuePlusResolver" as Resolver
participant "组件注册表" as Registry

Developer -> Vite: 编写组件使用代码
note right: <NeButton />

Vite -> Plugin: 解析 SFC 模板

Plugin -> Plugin: 检测未导入的组件
note right: 发现 NeButton 未导入

Plugin -> Resolver: resolveComponent('NeButton')

Resolver -> Resolver: 匹配组件名称规则
note right: /^Ne[A-Z]/

alt 匹配成功
  Resolver -> Registry: 查找组件信息
  Registry --> Resolver: 返回组件路径和配置

  Resolver -> Resolver: 确定导入路径
  note right: 'neue-plus' 或具体包路径

  alt 需要样式
    Resolver -> Resolver: 确定样式路径
    note right: CSS 或 Sass 文件
  end

  Resolver --> Plugin: 返回导入配置
  note right: { from: 'neue-plus', name: 'NeButton', sideEffects: [...] }

  Plugin -> Plugin: 生成导入语句
  note right: import { NeButton } from 'neue-plus'

else 匹配失败
  Resolver --> Plugin: 返回 null
end

Plugin --> Vite: 返回转换后的代码

Vite -> Vite: 编译和打包

Vite --> Developer: 运行时自动可用
@enduml
```
