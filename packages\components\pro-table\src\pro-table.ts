import { DynamicFormProps } from '@neue-plus/components/dynamic-form'
import { buildProp, buildProps } from '@neue-plus/utils'
import type { ColProps, PaginationProps, TableProps } from 'element-plus'
import type { ExtractPropTypes, PropType, VNodeChild } from 'vue'

type RequestParams = {
  pageSize: number
  current: number
  [key: string]: any
}
type SortOrder = 'ascend' | 'descend'

type Sorter = Record<string, SortOrder>

type Filter = Record<string, (string | number | boolean)[] | null>
type Request = (
  params: RequestParams,
  sort?: Sorter,
  filter?: Filter
) => Promise<{
  data: any[]
  total: number
  success: boolean
}>
export type ProFieldValueType =
  | 'password'
  | 'money'
  | 'textarea'
  | 'date'
  | 'dateTime'
  | 'dateWeek'
  | 'dateMonth'
  | 'dateQuarter'
  | 'dateYear'
  | 'dateRange'
  | 'dateTimeRange'
  | 'time'
  | 'timeRange'
  | 'text'
  | 'select'
  | 'treeSelect'
  | 'checkbox'
  | 'rate'
  | 'radio'
  | 'radioButton'
  | 'progress'
  | 'percent'
  | 'digit'
  | 'second'
  | 'avatar'
  | 'code'
  | 'switch'
  | 'fromNow'
  | 'image'
  | 'jsonCode'
  | 'color'
  | 'cascader'
  | 'segmented'
  | 'group'
  | 'formList'
  | 'formSet'
  | 'divider'
  | 'dependency'
/** 单个枚举项的定义 */
export interface ValueEnumItem {
  /** 展示的文本 */
  text: string
  /** 标签颜色状态，参考 ElementPlus / Ant Design 等组件库 */
  status?: 'success' | 'error' | 'processing' | 'default' | 'warning'
  /** 自定义颜色，例如 '#13c2c2' */
  color?: string
  /** 是否禁用该项 */
  disabled?: boolean
}

/** valueEnum 类型：键值对映射（key 可以是字符串或数字） */
export type ValueEnum = Record<string | number, ValueEnumItem>

// ProTable列配置，扩展了筛选功能
export interface ProTableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  // 筛选相关
  valueType?: ProFieldValueType
  valueEnum?: ValueEnum
  filterPlaceholder?: string
  // 在 Table 中不展示此列
  hideInTable?: boolean
  // 在 Form 中不展示此列
  hideInForm?: boolean
}

// 分页配置
export interface ProTablePagination extends Partial<PaginationProps> {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

// 筛选表单数据
export interface ProTableFilters {
  [key: string]: any
}

export type FilterType = 'query' | 'light'
export type ButtonAlign = 'left' | 'right' | 'center'

export interface SearchProps {
  /** 过滤表单类型（标准表单 or 精简表单） */
  filterType?: FilterType

  /** 查询按钮文本 */
  searchText?: string | false

  /** 重置按钮文本 */
  resetText?: string | false

  /** 提交按钮文本（可选） */
  submitText?: string | false

  /** 标签宽度：数字 或 'auto' */
  labelWidth?: number | 'auto'

  /** 表单列布局（数字表示栅格列数，也可使用 ColProps） */
  span?: number | Partial<ColProps>

  /** 自定义 class */
  className?: string

  /** 是否默认收起（用于响应式表单） */
  defaultCollapsed?: boolean

  /** 当前是否收起 */
  collapsed?: boolean

  /** 自定义“收起/展开”渲染函数，返回 false 关闭按钮 */
  collapseRender?: (
    collapsed: boolean,
    showCollapseButton?: boolean
  ) => VNodeChild | false

  /** 收起按钮事件 */
  onCollapse?: (collapsed: boolean) => void

  /** 自定义操作栏渲染，返回 false 可不渲染 */
  optionRender?: (
    searchConfig: SearchProps,
    formModel: Record<string, any>,
    dom: VNodeChild[]
  ) => VNodeChild[] | false

  /** 是否展示隐藏项个数提示 */
  showHiddenNum?: boolean

  buttonAlign?: ButtonAlign
}
export const proTableProps = buildProps({
  // 表格数据
  data: buildProp({
    type: Array,
    default: () => [],
  }),
  // 列配置
  columns: buildProp({
    type: Array as () => ProTableColumn[],
    required: true,
  }),
  // 是否显示筛选表单
  showFilter: buildProp({
    type: Boolean,
    default: true,
  }),
  // 搜索表单配置
  searchConfig: buildProp({
    type: Object as () => DynamicFormProps,
  }),
  // 是否显示分页
  showPagination: buildProp({
    type: Boolean,
    default: true,
  }),
  // 分页配置
  pagination: buildProp({
    type: Object as () => ProTablePagination,
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true,
    }),
  }),
  // 是否加载中
  loading: buildProp({
    type: Boolean,
    default: false,
  }),
  // 是否显示边框
  bordered: buildProp({
    type: Boolean,
    default: false,
  }),
  // 表格大小
  size: buildProp({
    type: String,
    values: ['large', 'default', 'small'],
    default: 'default',
  }),
  request: buildProp({
    type: Function as PropType<Request>,
    default: undefined,
  }),
} as const)

export type ProTableProps = ExtractPropTypes<typeof proTableProps> &
  Omit<TableProps<any>, 'data'>

// 事件类型 - 使用函数类型定义
export type ProTableEmits = {
  (e: 'filter-change', filters: ProTableFilters): void
  (e: 'page-change', current: number, pageSize: number): void
  (
    e: 'sort-change',
    prop: string,
    order: 'ascending' | 'descending' | null
  ): void
  (e: 'refresh'): void
}
