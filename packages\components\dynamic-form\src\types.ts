import NeCheckboxGroup from '@neue-plus/components/checkbox-group'
import { ButtonAlign, ProFieldValueType } from '@neue-plus/components/pro-table'
import NeRadioGroup from '@neue-plus/components/radio-group'
import type { FormItemProps } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

// 选项配置
export interface DynamicFormOption {
  label: string
  value: any
  disabled?: boolean
  children?: DynamicFormOption[]
}

// 表单项配置
export interface DynamicFormItem extends Partial<FormItemProps> {
  // 基础配置
  prop: string
  label: string
  valueType: ProFieldValueType

  // 显示控制
  show?: boolean
  disabled?: boolean

  // 表单项属性
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  filterable?: boolean

  // 选项配置
  options?: DynamicFormOption[]

  // 验证规则
  required?: boolean
  rules?: any[]

  // 输入框配置
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean

  // 数字输入框配置
  min?: number
  max?: number
  step?: number
  precision?: number

  // 日期配置
  format?: string
  valueFormat?: string

  // 上传配置
  action?: string
  accept?: string
  limit?: number

  // 级联选择器配置
  props?: any

  // 插槽名称
  slot?: string

  // 栅格布局
  span?: number
  offset?: number

  // 其他属性
  [key: string]: any
}

type SizeType = 'mini' | 'small' | 'medium' | 'large' | 'default'
export const dynamicFormProps = {
  inline: {
    type: Boolean,
    default: false,
  },
  // 表单数据
  modelValue: {
    type: Object as () => Record<string, any>,
    default: () => ({}),
  },

  // 是否加载中
  loading: {
    type: Boolean,
    default: false,
  },

  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 表单大小
  size: {
    type: String as () => SizeType,
    default: 'default',
  },
  // 表单配置
  formItems: {
    type: Array as () => DynamicFormItem[],
    required: true,
  },
  buttonAlign: {
    type: String as () => ButtonAlign,
    default: 'left',
  },
}

export type DynamicFormProps = ExtractPropTypes<typeof dynamicFormProps>
// 事件类型
export type DynamicFormEmits = {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'submit', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'cancel'): void
  (e: 'change', prop: string, value: any): void
  (e: 'validate', prop: string, isValid: boolean, message: string): void
}

export interface ComponentMap {
  input: string
  textarea: string
  select: string
  radio: typeof NeRadioGroup
  checkbox: typeof NeCheckboxGroup
  switch: string
  date: string
  daterange: string
  time: string
  number: string
  password: string
  upload: string
  cascader: string
  'tree-select': string
}
