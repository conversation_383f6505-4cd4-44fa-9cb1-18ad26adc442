interface FormConfig {
  layout?: 'horizontal' | 'vertical'
  labelWidth?: string
  gutter?: number
  showSubmit?: boolean
  showReset?: boolean
  showCancel?: boolean
  submitText?: string
  resetText?: string
  cancelText?: string
  buttonAlign?: 'left' | 'right' | 'center'
}

// 定义选项接口，用于 select、enum 和 array 类型的字段
interface Option {
  label: string
  value: string
}

// 定义基础字段接口
interface BaseField {
  fieldName: string
  prop: string
  fieldType?: string
  ruleId?: string
  required?: boolean
  filterable?: boolean
  options?: Option[]
}

// 定义特定字段类型接口，用于扩展基础字段接口
interface StringField extends BaseField {
  fieldType: 'string'
}

interface EmailField extends BaseField {
  fieldType: 'email'
}

interface NumberField extends BaseField {
  fieldType: 'number'
}

interface SelectField extends BaseField {
  fieldType: 'select'
  filterable: boolean
  options: Option[]
}

interface EnumField extends BaseField {
  fieldType: 'enum'
  options: Option[]
}

interface ArrayField extends BaseField {
  fieldType: 'array'
  options: Option[]
}

interface DateField extends BaseField {
  fieldType: 'date'
}

interface BooleanField extends BaseField {
  fieldType: 'boolean'
}

interface TextField extends BaseField {
  fieldType: 'textarea'
}

// 定义字段类型
type FieldType =
  | StringField
  | EmailField
  | NumberField
  | SelectField
  | EnumField
  | ArrayField
  | DateField
  | BooleanField
  | TextField

// 定义字段数组接口
interface FormField {
  formItems: FieldType[]
}

export type { FormConfig, FormField, FieldType, Option }

// 示例：定义一个具体的表单字段数组
// const formFields: FormField = {
//   formItems: [
//     {
//       fieldName: "姓名",
//       prop: "name",
//       fieldType: "string",
//       ruleId: "RULE_001",
//       required: true,
//     }
//   ]
// };
