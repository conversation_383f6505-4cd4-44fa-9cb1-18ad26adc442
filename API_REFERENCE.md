# Neue CDP Space API 参考文档

## 📦 核心 API

### MaterialRender 组件

MaterialRender 是低代码平台的核心渲染组件，负责将 JSON 配置转换为实际的 Vue 组件。

#### Props

```typescript
interface MaterialRenderProps {
  config?: {
    configProvider?: Record<string, any>  // Element Plus 全局配置
    events?: ElementEvent[]               // 页面级事件
  }
  elements: SchemaElement[]               // 组件树配置
  apis?: Record<string, ApiSchema>        // API 配置
}
```

#### 使用示例

```vue
<template>
  <NeMaterialRender
    :elements="elements"
    :apis="apis"
    :config="config"
    @before-render="handleBeforeRender"
    @after-render="handleAfterRender"
  />
</template>

<script setup>
const elements = [
  {
    id: 'table-1',
    type: 'pro-table',
    props: {
      columns: [
        { prop: 'name', label: '姓名' },
        { prop: 'age', label: '年龄' }
      ]
    }
  }
]

const apis = {
  tableData: {
    url: '/api/users',
    method: 'get'
  }
}

const config = {
  configProvider: {
    size: 'default',
    locale: 'zh-cn'
  }
}
</script>
```

### SchemaElement 接口

定义组件树中每个节点的结构。

```typescript
interface SchemaElement {
  id: string                      // 组件唯一标识
  type: string                    // 组件类型 (如 'ne-button', 'pro-table')
  name?: string                   // 组件显示名称
  props?: Record<string, any>     // 组件属性
  slots?: Record<string, any>     // 插槽内容
  elements?: SchemaElement[]      // 子组件
  events?: ElementEvent[]         // 组件事件
}
```

#### 示例

```typescript
const buttonElement: SchemaElement = {
  id: 'btn-1',
  type: 'ne-button',
  name: '提交按钮',
  props: {
    type: 'primary',
    size: 'large',
    disabled: false
  },
  events: [
    {
      nickName: '点击事件',
      eventName: 'onClick',
      actions: [
        {
          id: 'action-1',
          type: 'normal',
          title: '显示消息',
          config: {
            actionType: 'showMessage',
            message: '按钮被点击了！'
          }
        }
      ]
    }
  ]
}
```

## 🔄 EventFlow API

### useEventFlow Hook

用于在组件中执行事件流。

```typescript
function useEventFlow(): {
  run: (actions: ActionNode[], params?: Record<string, any>) => void
}
```

#### 参数

- `actions`: 事件动作数组
- `params`: 传递给事件处理器的参数

#### 示例

```typescript
import { useEventFlow } from '@neue-plus/components/material-render'

const eventFlow = useEventFlow()

const handleSubmit = () => {
  eventFlow.run([
    {
      config: {
        actionType: 'validateForm',
        target: 'form-1'
      }
    },
    {
      config: {
        actionType: 'submitData',
        delay: 300
      }
    }
  ], {
    formData: { name: 'John', age: 30 }
  })
}
```

### ActionNode 接口

定义事件流中的单个动作节点。

```typescript
interface ActionNode {
  id?: string                     // 节点ID
  type?: 'start' | 'end' | 'normal'  // 节点类型
  title?: string                  // 节点标题
  content?: string                // 节点描述
  config: {
    actionType: string            // 动作类型
    target?: string               // 目标组件ID
    delay?: number                // 延迟执行时间(ms)
    [key: string]: any           // 其他配置参数
  }
}
```

### 事件上下文 API

#### provideEventContext

在父组件中提供事件上下文。

```typescript
function provideEventContext(
  handlers: Record<string, Function>,
  apis?: Record<string, ApiSchema>
): Record<string, any>
```

#### useEventContext

在子组件中获取事件上下文。

```typescript
function useEventContext(): {
  refs: Record<string, any>
  handlers: Record<string, Function>
  apis: Record<string, ApiSchema>
}
```

## 🌐 API 服务

### executeApi 函数

执行 API 调用的核心函数。

```typescript
async function executeApi(
  api: ApiSchema,
  context: Record<string, any>
): Promise<any>
```

#### 参数

- `api`: API 配置对象
- `context`: 上下文数据，用于表达式解析

#### 返回值

返回 API 调用的结果数据。

### ApiSchema 接口

定义 API 调用的配置结构。

```typescript
interface ApiSchema {
  url: string                     // API 地址
  method: 'get' | 'post' | 'put' | 'delete'  // HTTP 方法
  protocol?: 'rest' | 'odata'     // 协议类型
  headers?: Record<string, any>   // 请求头
  params?: Record<string, any>    // 查询参数
  body?: any                      // 请求体
  dataPath?: string               // 数据提取路径
  map?: {                         // 数据映射配置
    label: string
    value: string
  }
  
  // OData 专用配置
  odata?: {
    filter?: string               // 过滤条件
    orderby?: string              // 排序字段
    select?: string               // 选择字段
    expand?: string               // 展开关联
    top?: number                  // 返回数量
    skip?: number                 // 跳过数量
    count?: boolean               // 是否返回总数
  }
}
```

#### REST API 示例

```typescript
const restApi: ApiSchema = {
  url: '/api/users',
  method: 'get',
  headers: {
    'Authorization': 'Bearer {{token}}'
  },
  params: {
    page: '{{currentPage}}',
    size: 20,
    keyword: '{{searchKeyword}}'
  },
  dataPath: 'data.list'
}
```

#### OData API 示例

```typescript
const odataApi: ApiSchema = {
  url: 'https://services.odata.org/V4/TripPinServiceRW/People',
  method: 'get',
  protocol: 'odata',
  odata: {
    select: 'FirstName,LastName,Gender,Age',
    filter: "Age gt 18 and Gender eq 'Male'",
    orderby: 'FirstName asc',
    top: 10,
    skip: 0,
    count: true
  },
  dataPath: 'value'
}
```

## 🧩 组件 API

### 基础组件

#### NeButton

```typescript
interface NeButtonProps {
  size?: 'large' | 'default' | 'small'
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  plain?: boolean
  round?: boolean
  circle?: boolean
  loading?: boolean
  disabled?: boolean
  icon?: string | Component
  autofocus?: boolean
  nativeType?: 'button' | 'submit' | 'reset'
}
```

#### NeTable

```typescript
interface NeTableProps {
  data: any[]
  columns: TableColumn[]
  height?: string | number
  maxHeight?: string | number
  stripe?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
  fit?: boolean
  showHeader?: boolean
  highlightCurrentRow?: boolean
  currentRowKey?: string | number
  rowClassName?: string | ((row: any, index: number) => string)
  rowStyle?: object | ((row: any, index: number) => object)
  cellClassName?: string | ((row: any, column: any, rowIndex: number, columnIndex: number) => string)
  cellStyle?: object | ((row: any, column: any, rowIndex: number, columnIndex: number) => object)
  headerRowClassName?: string | ((row: any, index: number) => string)
  headerRowStyle?: object | ((row: any, index: number) => object)
  headerCellClassName?: string | ((row: any, column: any, rowIndex: number, columnIndex: number) => string)
  headerCellStyle?: object | ((row: any, column: any, rowIndex: number, columnIndex: number) => object)
  rowKey?: string | ((row: any) => string)
  emptyText?: string
  defaultExpandAll?: boolean
  expandRowKeys?: (string | number)[]
  defaultSort?: { prop: string; order: 'ascending' | 'descending' }
  tooltipEffect?: 'dark' | 'light'
  showSummary?: boolean
  sumText?: string
  summaryMethod?: (columns: any[], data: any[]) => string[]
  spanMethod?: (row: any, column: any, rowIndex: number, columnIndex: number) => number[] | { rowspan: number; colspan: number }
  selectOnIndeterminate?: boolean
  indent?: number
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  treeProps?: { hasChildren?: string; children?: string }
}

interface TableColumn {
  prop?: string
  label?: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  renderHeader?: (column: any, index: number) => VNode
  sortable?: boolean | 'custom'
  sortMethod?: (a: any, b: any) => number
  sortBy?: string | string[] | ((row: any, index: number) => string)
  resizable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  showOverflowTooltip?: boolean
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  className?: string
  labelClassName?: string
  selectable?: (row: any, index: number) => boolean
  reserveSelection?: boolean
  filters?: { text: string; value: any }[]
  filterPlacement?: string
  filterMultiple?: boolean
  filterMethod?: (value: any, row: any, column: any) => boolean
  filteredValue?: any[]
}
```

#### NeProTable

ProTable 是增强版的表格组件，支持搜索、分页、排序等功能。

```typescript
interface NeProTableProps extends NeTableProps {
  columns: ProTableColumn[]
  searchConfig?: SearchConfig
  paginationConfig?: PaginationConfig
  toolbarConfig?: ToolbarConfig
  requestConfig?: RequestConfig
}

interface ProTableColumn extends TableColumn {
  search?: boolean | SearchColumnConfig
  hideInTable?: boolean
  hideInSearch?: boolean
  valueType?: 'text' | 'select' | 'date' | 'dateRange' | 'number'
  valueEnum?: Record<string, { text: string; status?: string }>
  request?: (params: any) => Promise<{ label: string; value: any }[]>
}

interface SearchConfig {
  labelWidth?: string | number
  collapsed?: boolean
  collapseRender?: boolean
  defaultCollapsed?: boolean
  optionRender?: boolean | ((searchConfig: any, formProps: any, dom: VNode[]) => VNode[])
  searchText?: string
  resetText?: string
  submitText?: string
  span?: number | Record<string, number>
}
```

### 业务组件 (Widgets)

#### WidgetBasicForm

基础表单 Widget，用于快速构建表单界面。

```typescript
interface WidgetBasicFormProps {
  columns: FormColumn[]
  data?: Record<string, any>
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'
  inline?: boolean
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  validateOnRuleChange?: boolean
  hideRequiredAsterisk?: boolean
  showMessage?: boolean
  inlineMessage?: boolean
  statusIcon?: boolean
  scrollToError?: boolean
}

interface FormColumn {
  prop: string
  label: string
  type?: 'input' | 'select' | 'date' | 'checkbox' | 'radio' | 'textarea'
  required?: boolean
  rules?: FormItemRule[]
  placeholder?: string
  options?: { label: string; value: any }[]
  span?: number
  offset?: number
  push?: number
  pull?: number
  xs?: number | { span?: number; offset?: number }
  sm?: number | { span?: number; offset?: number }
  md?: number | { span?: number; offset?: number }
  lg?: number | { span?: number; offset?: number }
  xl?: number | { span?: number; offset?: number }
}
```

## 🎨 主题 API

### useNamespace Hook

用于生成 BEM 风格的 CSS 类名。

```typescript
function useNamespace(block: string): {
  b: () => string                           // 块级类名
  e: (element: string) => string            // 元素类名
  m: (modifier: string) => string           // 修饰符类名
  be: (element: string, modifier: string) => string  // 块-元素类名
  em: (element: string, modifier: string) => string  // 元素-修饰符类名
  bm: (modifier: string) => string          // 块-修饰符类名
  bem: (element: string, modifier: string) => string // 块-元素-修饰符类名
  is: (name: string, state?: boolean) => string      // 状态类名
}
```

#### 使用示例

```typescript
const ns = useNamespace('button')

// 生成类名
ns.b()                    // 'ne-button'
ns.e('text')             // 'ne-button__text'
ns.m('primary')          // 'ne-button--primary'
ns.be('text', 'primary') // 'ne-button__text--primary'
ns.is('disabled', true)  // 'is-disabled'
```

### CSS 变量 API

项目使用 CSS 自定义属性来管理主题变量。

```scss
// 获取 CSS 变量
.my-component {
  color: getCssVar('color-primary');
  font-size: getCssVar('font-size-base');
  padding: getCssVar('space-md');
}

// 设置 CSS 变量
:root {
  --ne-color-primary: #409eff;
  --ne-color-success: #67c23a;
  --ne-font-size-base: 14px;
  --ne-space-md: 16px;
}
```

## 🔧 工具 API

### buildProps 函数

用于构建组件 props 定义的工具函数。

```typescript
function buildProps<T extends Record<string, any>>(
  props: T
): T

// 使用示例
export const buttonProps = buildProps({
  size: {
    type: String,
    values: ['large', 'default', 'small'],
    default: 'default',
  },
  type: {
    type: String,
    values: ['primary', 'success', 'warning', 'danger', 'info', 'text'],
    default: '',
  },
  disabled: Boolean,
} as const)
```

### withInstall 函数

为组件添加 install 方法，使其可以作为 Vue 插件使用。

```typescript
function withInstall<T>(component: T): T & { install: (app: App) => void }

// 使用示例
import Button from './button.vue'

export const NeButton = withInstall(Button)
```

---

*API 参考文档 - 最后更新: 2025-01-21*
