declare module 'vue' {
  // GlobalComponents for Volar
  export interface GlobalComponents {
    NeCard: typeof import('neue-plus')['NeCard']
    NeTable: typeof import('neue-plus')['NeTable']
    NeProTable: typeof import('neue-plus')['NeProTable']
    NeDynamicForm: typeof import('neue-plus')['NeDynamicForm']
    NeRadioGroup: typeof import('neue-plus')['NeRadioGroup']
    NeConfigProvider: typeof import('neue-plus')['NeConfigProvider']
    NeTooltipEllipsis: typeof import('neue-plus')['NeTooltipEllipsis']
    NeAvatar: typeof import('neue-plus')['NeAvatar']
    NeWidgetWrapper: typeof import('neue-plus')['NeWidgetWrapper']
    NeWidgetContent: typeof import('neue-plus')['NeWidgetContent']
  }

  interface ComponentCustomProperties {
    $message: typeof import('neue-plus')['ElMessage']
  }
}

export {}
