import { parseExpression } from '../parser'

export function buildODataParams(odata: any, context: Record<string, any>) {
  const result: Record<string, any> = {}

  if (odata.filter) result['$filter'] = parseExpression(odata.filter, context)
  if (odata.orderby) result['$orderby'] = odata.orderby
  if (odata.select) result['$select'] = odata.select
  if (odata.expand) result['$expand'] = odata.expand
  if (odata.top != null) result['$top'] = odata.top
  if (odata.skip != null) result['$skip'] = odata.skip
  if (odata.count) result['$count'] = 'true'

  return result
}
