import { executeApi } from '../../../../services/executor'
import type { ApiSchema } from '../../../../services/types'

/**
 * 根据 schema 封装一个可调用的 API 方法
 * @param apiSchema 接口 schema（支持 OData/rest）
 * @returns (params) => Promise<any>
 */
export function useApiFlow(apiSchema: ApiSchema) {
  return async function (params: Record<string, any> = {}) {
    // 合并 schema 默认参数和调用时传参
    const mergedSchema = {
      ...apiSchema,
      params: { ...(apiSchema.params || {}), ...(params || {}) },
    }
    return executeApi(mergedSchema, params)
  }
}
