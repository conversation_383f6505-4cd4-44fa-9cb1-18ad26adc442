# Neue CDP Space 开发指南

## 🚀 快速开始

### 环境准备

```bash
# 检查 Node.js 版本 (需要 >= 20)
node --version

# 检查 pnpm 版本 (需要 >= 9.5.0)
pnpm --version

# 如果没有 pnpm，请安装
npm install -g pnpm
```

### 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd neue-cdp-space

# 安装依赖
pnpm install

# 构建项目
pnpm build

# 启动开发环境
pnpm dev
```

## 📁 开发环境说明

### Playground 环境

`play/` 目录是项目的开发调试环境：

```bash
# 启动 playground
pnpm dev

# 访问地址
http://localhost:5173
```

### 目录结构

```
play/
├── src/
│   ├── App.vue           # 主应用组件
│   ├── main.ts           # 应用入口
│   ├── constant/         # 测试数据和配置
│   │   └── index.ts      # 各种测试配置
│   └── style.css         # 全局样式
├── public/               # 静态资源
├── index.html            # HTML 模板
└── vite.config.ts        # Vite 配置
```

## 🧩 组件开发

### 1. 创建基础组件

#### 步骤 1: 创建组件目录

```bash
mkdir -p packages/components/your-component/src
mkdir -p packages/components/your-component/style
```

#### 步骤 2: 编写组件

```vue
<!-- packages/components/your-component/src/your-component.vue -->
<template>
  <div :class="ns.b()" v-bind="$attrs">
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { useNamespace } from '@neue-plus/hooks'
import { yourComponentProps } from './types'

defineOptions({
  name: 'NeYourComponent',
  inheritAttrs: false,
})

const props = defineProps(yourComponentProps)
const ns = useNamespace('your-component')

// 暴露组件方法供 EventFlow 使用
defineExpose({
  doSomething: () => {
    console.log('Component method called')
  },
})
</script>
```

#### 步骤 3: 定义类型

```typescript
// packages/components/your-component/src/types.ts
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

export const yourComponentProps = buildProps({
  size: {
    type: String,
    values: ['large', 'default', 'small'],
    default: 'default',
  },
  disabled: Boolean,
} as const)

export type YourComponentProps = ExtractPropTypes<typeof yourComponentProps>
```

#### 步骤 4: 样式文件

```typescript
// packages/components/your-component/style/index.ts
import '@neue-plus/theme-chalk/es/your-component.css'
```

```scss
// packages/theme-chalk/src/your-component.scss
@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(your-component) {
  display: inline-block;
  
  @include when(disabled) {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  @include m(large) {
    font-size: getCssVar('font-size-large');
  }
}
```

#### 步骤 5: 导出组件

```typescript
// packages/components/your-component/index.ts
import { withInstall } from '@neue-plus/utils'
import YourComponent from './src/your-component.vue'

export const NeYourComponent = withInstall(YourComponent)
export default NeYourComponent

export * from './src/types'
```

```typescript
// packages/components/index.ts
export * from './your-component'
```

### 2. 创建业务组件 (Widget)

#### 步骤 1: 创建 Widget 目录

```bash
mkdir -p packages/widgets/your-widget/src
```

#### 步骤 2: 编写 Widget

```vue
<!-- packages/widgets/your-widget/src/widget.vue -->
<template>
  <widget-wrapper>
    <div :class="ns.b()">
      <!-- Widget 内容 -->
      <NeTable v-bind="tableProps" />
    </div>
  </widget-wrapper>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useNamespace } from '@neue-plus/hooks'
import { NeTable } from '@neue-plus/components'
import WidgetWrapper from '../widget-wrapper'
import { yourWidgetProps } from './types'

defineOptions({
  name: 'WidgetYourWidget',
})

const props = defineProps(yourWidgetProps)
const ns = useNamespace('widget-your-widget')

const tableProps = computed(() => ({
  data: props.data,
  columns: props.columns,
}))

// 暴露 Widget 方法
defineExpose({
  refresh: () => {
    // 刷新数据逻辑
  },
  exportData: () => {
    // 导出数据逻辑
  },
})
</script>
```

#### 步骤 3: 注册 Widget

```typescript
// packages/widgets/index.ts
export * from './your-widget'

// packages/widgets/registry.ts
import { WidgetYourWidget } from './your-widget'

export const widgetRegistry = {
  'wg-your-widget': WidgetYourWidget,
  // ...其他 widgets
}
```

## 🔄 EventFlow 开发

### 1. 定义事件处理器

```typescript
// packages/components/material-render/src/handlers/index.ts
export const showMessage = (action: any, params: any) => {
  ElMessage.success(params.message || '操作成功')
}

export const openDialog = (action: any, params: any, refs: any) => {
  const target = refs[action.config.target]
  if (target && target.open) {
    target.open()
  }
}
```

### 2. 组件中使用 EventFlow

```vue
<script setup>
import { useEventFlow } from '@neue-plus/components/material-render'

const eventFlow = useEventFlow()

const handleClick = () => {
  eventFlow.run([
    {
      config: {
        actionType: 'showMessage',
        delay: 0,
      },
    },
    {
      config: {
        actionType: 'openDialog',
        target: 'dialog-1',
        delay: 300,
      },
    },
  ], {
    message: '点击成功！',
  })
}
</script>
```

## 🌐 API 服务开发

### 1. 定义 API Schema

```typescript
// 在配置中定义 API
const apiConfig = {
  getUserList: {
    url: '/api/users',
    method: 'get',
    protocol: 'rest',
    params: {
      page: '{{page}}',
      size: '{{size}}',
    },
    dataPath: 'data.list',
  },
  
  getODataUsers: {
    url: 'https://services.odata.org/V4/TripPinServiceRW/People',
    method: 'get',
    protocol: 'odata',
    odata: {
      select: 'FirstName,LastName,Gender',
      filter: "Gender eq 'Male'",
      top: 10,
    },
    dataPath: 'value',
  },
}
```

### 2. 在组件中使用 API

```vue
<script setup>
import { ref, onMounted } from 'vue'
import { executeApi } from '@neue-plus/services'

const data = ref([])
const loading = ref(false)

const loadData = async () => {
  loading.value = true
  try {
    const result = await executeApi(apiConfig.getUserList, {
      page: 1,
      size: 20,
    })
    data.value = result
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(loadData)
</script>
```

## 🎨 样式开发

### 1. 使用设计令牌

```scss
@use '@neue-plus/theme-chalk/src/mixins/mixins' as *;
@use '@neue-plus/theme-chalk/src/common/var' as *;

@include b(my-component) {
  color: getCssVar('color-primary');
  font-size: getCssVar('font-size-base');
  padding: getCssVar('space-md');
  
  @include e(header) {
    font-weight: getCssVar('font-weight-bold');
    border-bottom: 1px solid getCssVar('border-color-light');
  }
  
  @include m(large) {
    font-size: getCssVar('font-size-large');
  }
  
  @include when(disabled) {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
```

### 2. 响应式设计

```scss
@include b(responsive-component) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: getCssVar('space-lg');
  
  @include respond-to('tablet') {
    grid-template-columns: 1fr;
    gap: getCssVar('space-md');
  }
  
  @include respond-to('mobile') {
    padding: getCssVar('space-sm');
  }
}
```

## 🧪 测试开发

### 1. 单元测试

```typescript
// packages/components/your-component/__tests__/your-component.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import YourComponent from '../src/your-component.vue'

describe('YourComponent', () => {
  it('renders correctly', () => {
    const wrapper = mount(YourComponent, {
      props: {
        size: 'large',
      },
    })
    
    expect(wrapper.classes()).toContain('ne-your-component')
    expect(wrapper.classes()).toContain('ne-your-component--large')
  })
  
  it('emits events correctly', async () => {
    const wrapper = mount(YourComponent)
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 2. 组件测试

```typescript
// packages/components/your-component/__tests__/integration.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import { createApp } from 'vue'
import YourComponent from '../src/your-component.vue'

describe('YourComponent Integration', () => {
  it('works with EventFlow', async () => {
    const app = createApp({
      template: '<YourComponent ref="component" />',
      components: { YourComponent },
    })
    
    const wrapper = mount(app)
    const component = wrapper.findComponent({ ref: 'component' })
    
    // 测试 defineExpose 的方法
    expect(component.vm.doSomething).toBeDefined()
    
    // 调用方法
    component.vm.doSomething()
  })
})
```

## 🔧 调试技巧

### 1. Vue DevTools

```typescript
// 在开发环境中启用调试信息
if (process.env.NODE_ENV === 'development') {
  app.config.globalProperties.$debug = {
    eventFlow: true,
    apiCalls: true,
    componentRegistry: true,
  }
}
```

### 2. 控制台调试

```typescript
// 在组件中添加调试信息
const debugLog = (message: string, data?: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${componentName}] ${message}`, data)
  }
}

// EventFlow 调试
eventFlow.run(actions, params, { debug: true })
```

### 3. 性能分析

```typescript
// 性能监控
const startTime = performance.now()

// 执行操作...

const endTime = performance.now()
console.log(`操作耗时: ${endTime - startTime}ms`)
```

## 📦 构建和发布

### 1. 本地构建

```bash
# 清理构建产物
pnpm clean

# 构建所有包
pnpm build

# 构建特定包
pnpm -C packages/components build
```

### 2. 类型检查

```bash
# 检查所有类型
pnpm typecheck

# 检查特定配置
pnpm typecheck:web
pnpm typecheck:play
```

### 3. 代码质量检查

```bash
# ESLint 检查
pnpm lint

# 自动修复
pnpm lint:fix

# Prettier 格式化
pnpm format
```

## 🚀 最佳实践

### 1. 组件设计原则

- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 通过 props 和 slots 提供灵活性
- **可访问性**: 遵循 ARIA 标准
- **性能优化**: 合理使用 v-memo 和 shallowRef

### 2. 代码规范

- 使用 TypeScript 进行类型约束
- 遵循 Vue 3 Composition API 最佳实践
- 使用 ESLint 和 Prettier 保持代码风格一致
- 编写有意义的注释和文档

### 3. 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证组件交互
- E2E 测试确保用户流程正常

---

*开发指南 - 最后更新: 2025-01-21*
