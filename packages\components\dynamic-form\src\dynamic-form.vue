<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<template>
  <el-form ref="formRef" :model="formData" v-bind="attrs">
    <el-row :gutter="props.span || 3">
      <el-col
        v-for="item in visibleItems"
        :key="item.prop"
        v-bind="item.colProps"
      >
        <el-form-item v-bind="item" :rules="getItemRules(item)">
          <!-- 自定义渲染 -->
          <component
            :is="getFormItemComponent(item.valueType)"
            v-bind="item"
            v-model="formData[item.prop]"
            @change="(value: any) => handleChange(item.prop, value)"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 操作按钮 -->
    <el-form-item :style="{ textAlign: props.buttonAlign }">
      <el-button
        v-if="config?.searchText === false"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ config.searchText || '查询' }}
      </el-button>
      <el-button v-if="config?.resetText === false" @click="handleReset">
        {{ config.resetText || '重置' }}
      </el-button>

      <slot name="extra-buttons" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { computed, ref, useAttrs, watch } from 'vue'
import NeRadioGroup from '@neue-plus/components/radio-group'
import NeCheckboxGroup from '@neue-plus/components/checkbox-group'
import { type DynamicFormItem, dynamicFormProps } from './types'
import type { ComponentMap } from './types'

defineOptions({
  name: 'NeDynamicForm',
  inheritAttrs: false,
})

const props = defineProps(dynamicFormProps)
const attrs = useAttrs()

const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'cancel',
  'change',
  'validate',
])

const formRef = ref()
const formData = ref<Record<string, any>>({})

// 可见的表单项
const visibleItems = computed(() => {
  return props.formItems
    ?.filter((item) => item.show !== false)
    .map((item) => {
      const { span = 24, offset = 0, ...restProps } = item
      return {
        colProps: { span, offset },
        ...restProps,
      }
    })
})

// 获取表单项组件
const getFormItemComponent = (type: string) => {
  const componentMap: ComponentMap = {
    input: 'el-input',
    textarea: 'el-input',
    select: 'el-select',
    radio: NeRadioGroup,
    checkbox: NeCheckboxGroup,
    switch: 'el-switch',
    date: 'el-date-picker',
    daterange: 'el-date-picker',
    time: 'el-time-picker',
    number: 'el-input-number',
    password: 'el-input',
    upload: 'el-upload',
    cascader: 'el-cascader',
    'tree-select': 'el-tree-select',
  }
  return componentMap[type as keyof ComponentMap] || 'el-input'
}

// 获取表单项规则
const getItemRules = (item: DynamicFormItem) => {
  const rules = [...(item.rules || [])]

  if (item.required) {
    rules.unshift({
      required: true,
      message: `请输入${item.label}`,
      trigger: ['blur', 'change'],
    })
  }
  return rules
}

// 处理值变化
const handleChange = (prop: string, value: any) => {
  formData.value[prop] = value
  emit('update:modelValue', formData.value)
  emit('change', prop, value)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', formData.value)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

// 获取默认值
const getDefaultValue = (type: string) => {
  switch (type) {
    case 'radio':
    case 'checkbox':
    case 'daterange':
      return []
    case 'switch':
      return false
    case 'number':
      return 0
    default:
      return ''
  }
}
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...formData.value, ...newValue }
  },
  { immediate: true, deep: true }
)

// 初始化表单数据
watch(
  () => props.formItems,
  (items) => {
    const newFormData = { ...formData.value }
    items?.forEach((item) => {
      if (!(item.prop in newFormData)) {
        newFormData[item.prop] = getDefaultValue(item.type)
      }
    })
    formData.value = newFormData
  },
  { immediate: true }
)

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
})
</script>
